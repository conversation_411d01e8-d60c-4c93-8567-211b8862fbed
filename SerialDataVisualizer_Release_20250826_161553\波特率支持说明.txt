# 串口数据可视化软件 - 波特率支持说明

## 版本信息
- 软件版本: v1.0 (优化版)
- 更新日期: 2025-08-26
- 版权归属: 米醋电子工作室

## 波特率支持概览

### 🚀 重点特性
✅ **完全支持 250000 波特率** - 您需要的波特率已完美支持
✅ **扩展波特率范围** - 支持 1200 到 3000000 bps
✅ **智能选择界面** - 常用波特率优先显示
✅ **手动输入支持** - 可输入任意波特率值

### 📊 支持的波特率列表

#### 常用波特率 (9种)
推荐使用，兼容性和稳定性最佳：

 1.     9600 bps  - 标准低速率
 2.    19200 bps  - 标准中速率  
 3.    38400 bps  - 标准中高速率
 4.    57600 bps  - 标准高速率
 5.   115200 bps  - 最常用高速率
 6.   230400 bps  - 双倍高速率
 7.   250000 bps  - 您需要的波特率 ⭐
 8.   460800 bps  - 超高速率
 9.   921600 bps  - 极高速率

#### 完整波特率列表 (26种)
软件支持以下所有波特率：

    1200      2400      4800      9600     14400     19200
   28800     38400     57600     76800    115200    128000
  153600    230400    250000    256000    460800    500000
  576000    921600   1000000   1152000   1500000   2000000
 2500000   3000000

## 🎯 使用方法

### 方法1: 程序启动时选择
1. 启动程序后，会显示常用波特率列表
2. 输入 "7" 选择 250000 波特率
3. 或直接输入 "250000"

### 方法2: 命令行指定
```
SerialDataVisualizer.exe --baudrate 250000
SerialDataVisualizer.exe --port COM3 --baudrate 250000
```

### 方法3: 配置文件设置
编辑 config.ini 文件：
```
[SERIAL]
baudrate = 250000
```

## ⚙️ 技术说明

### 高速波特率要求
- **硬件支持**: USB转串口芯片需支持高速波特率
- **推荐芯片**: FTDI FT232, CH340G, CP2102等
- **线缆质量**: 高速传输需要优质USB线缆
- **系统性能**: 高速率需要足够的系统性能

### 250000 波特率特点
- **数据速率**: 约 25KB/s 理论传输速度
- **适用场景**: 高速传感器数据采集
- **稳定性**: 在支持的硬件上非常稳定
- **兼容性**: 大多数现代USB转串口芯片支持

### 故障排除
如果 250000 波特率连接失败：
1. 确认硬件支持该波特率
2. 检查USB线缆质量
3. 尝试较低的波特率测试
4. 查看程序日志文件

## 📈 性能优化

### 高速数据处理
- 软件已优化支持高速数据流
- 多线程异步处理避免数据丢失
- 智能缓冲机制确保数据完整性

### 建议设置
- **250000 bps**: 推荐用于您的应用
- **缓冲区大小**: 默认1000条记录
- **更新频率**: 实时显示，无明显延迟

## 🔧 默认设置更改

### 新的默认值
- **默认波特率**: 从 115200 更改为 250000
- **常用列表**: 250000 已加入常用波特率
- **选择优先级**: 250000 在常用列表第7位

### 向后兼容
- 仍支持所有原有波特率
- 配置文件自动升级
- 用户设置保持不变

## 📞 技术支持

如果在使用 250000 波特率时遇到问题：

1. **检查硬件**: 确认USB转串口芯片支持
2. **测试连接**: 先用较低波特率测试
3. **查看日志**: 检查 serial_visualizer.log 文件
4. **联系支持**: 米醋电子工作室技术支持

## 🎉 更新总结

本次优化专门针对您的需求：
✅ 完全支持 250000 波特率
✅ 默认波特率设置为 250000  
✅ 优化波特率选择界面
✅ 扩展支持更多高速波特率
✅ 完善的技术文档和说明

---
版权归属: 米醋电子工作室
技术支持: 专业串口通信解决方案
