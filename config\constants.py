#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
常量定义模块
版权归属: 米醋电子工作室
"""

# 应用程序信息
APP_NAME = "串口数据可视化软件"
APP_VERSION = "1.0.0"
APP_AUTHOR = "米醋电子工作室"

# 串口参数 - 扩展波特率支持
SERIAL_BAUDRATES = [
    1200, 2400, 4800, 9600, 14400, 19200, 28800, 38400,
    57600, 76800, 115200, 128000, 153600, 230400, 250000,
    256000, 460800, 500000, 576000, 921600, 1000000, 1152000,
    1500000, 2000000, 2500000, 3000000
]

# 常用波特率 (用于快速选择)
COMMON_BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 250000, 460800, 921600]
SERIAL_DATABITS = [5, 6, 7, 8]
SERIAL_STOPBITS = [1, 1.5, 2]
SERIAL_PARITIES = ['N', 'E', 'O', 'M', 'S']  # None, Even, Odd, Mark, Space

# 数据格式
DATA_PACKET_SIZE = 10  # 每个数据包包含10个数值
SENSOR_COUNT = 5       # 前5个为传感器数据
EXTENDED_COUNT = 5     # 后5个为扩展数据

# 传感器位置 (x, y)
SENSOR_POSITIONS = [
    (0.5, 0.5),  # 传感器1 - 中心
    (1.0, 1.0),  # 传感器2 - 右上
    (1.0, 0.0),  # 传感器3 - 右下
    (0.0, 1.0),  # 传感器4 - 左上
    (0.0, 0.0),  # 传感器5 - 左下
]

# 颜色定义 (RGBA)
COLORS = {
    'BACKGROUND': (0.2, 0.2, 0.2, 1.0),
    'GRID': (0.5, 0.5, 0.5, 0.3),
    'AXES': (1.0, 1.0, 1.0, 0.8),
    'SENSOR_NORMAL': (0.0, 1.0, 0.0, 0.8),    # 绿色
    'SENSOR_WARNING': (1.0, 1.0, 0.0, 0.8),   # 黄色
    'SENSOR_CRITICAL': (1.0, 0.0, 0.0, 0.8),  # 红色
    'TEXT': (1.0, 1.0, 1.0, 1.0),
}

# 3D渲染参数
RENDER_SETTINGS = {
    'SPHERE_RADIUS': 0.05,
    'SPHERE_SEGMENTS': 16,
    'GRID_SIZE': 10,
    'AXIS_LENGTH': 1.2,
    'CAMERA_DISTANCE': 3.0,
    'FOV': 45.0,
    'NEAR_PLANE': 0.1,
    'FAR_PLANE': 100.0,
}

# 性能参数
PERFORMANCE = {
    'MAX_FPS': 60,
    'DATA_BUFFER_SIZE': 1000,
    'UI_UPDATE_INTERVAL': 33,  # 约30fps
    'SERIAL_TIMEOUT': 1.0,
    'RECONNECT_INTERVAL': 5.0,
}

# 文件路径
PATHS = {
    'CONFIG_FILE': 'config.ini',
    'LOG_FILE': 'serial_visualizer.log',
    'DATA_DIR': 'data',
    'EXPORT_DIR': 'exports',
}

# 数据范围
DATA_RANGES = {
    'SENSOR_MIN': 0,
    'SENSOR_MAX': 4000,
    'Z_SCALE_FACTOR': 0.001,  # 将传感器数值缩放到合适的Z轴高度
}

# UI尺寸
UI_SIZES = {
    'WINDOW_MIN_WIDTH': 800,
    'WINDOW_MIN_HEIGHT': 600,
    'CONTROL_PANEL_WIDTH': 300,
    'STATUS_BAR_HEIGHT': 25,
    'TOOLBAR_HEIGHT': 40,
}
