#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口模块
版权归属: 米醋电子工作室
"""

import os
from PyQt5.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, 
                            QMenuBar, QStatusBar, QAction, QMessageBox, 
                            QFileDialog, QToolBar, QSplitter)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QIcon, QKeySequence
from ui.opengl_widget import OpenGL3DWidget
from ui.control_panel import ControlPanel
from core.data_controller import DataController
from config.settings import AppConfig
from config.constants import APP_NAME, APP_VERSION, UI_SIZES
from utils.logger import get_logger


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger()
        
        # 初始化组件
        self.config = AppConfig()
        self.data_controller = DataController()
        
        # UI组件
        self.opengl_widget = None
        self.control_panel = None
        self.status_bar = None
        
        # 状态变量
        self.is_fullscreen = False
        
        # 初始化UI
        self.init_ui()
        self.setup_connections()
        self.setup_status_bar()
        
        # 状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新一次状态
        
        self.logger.info("主窗口初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性
        self.setWindowTitle(f"{APP_NAME} v{APP_VERSION}")
        self.setMinimumSize(UI_SIZES['WINDOW_MIN_WIDTH'], UI_SIZES['WINDOW_MIN_HEIGHT'])
        
        # 从配置加载窗口大小
        display_config = self.config.get_display_config()
        self.resize(display_config['window_width'], display_config['window_height'])
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 创建3D可视化组件
        self.opengl_widget = OpenGL3DWidget()
        splitter.addWidget(self.opengl_widget)
        
        # 创建控制面板
        self.control_panel = ControlPanel()
        splitter.addWidget(self.control_panel)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 3)  # 3D视图占更多空间
        splitter.setStretchFactor(1, 1)  # 控制面板固定宽度
        
        main_layout.addWidget(splitter)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_toolbar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')
        
        # 导出数据
        export_action = QAction('导出数据(&E)', self)
        export_action.setShortcut(QKeySequence.Save)
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut(QKeySequence.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 视图菜单
        view_menu = menubar.addMenu('视图(&V)')
        
        # 全屏
        fullscreen_action = QAction('全屏(&F)', self)
        fullscreen_action.setShortcut(QKeySequence.FullScreen)
        fullscreen_action.triggered.connect(self.toggle_fullscreen)
        view_menu.addAction(fullscreen_action)
        
        # 重置视图
        reset_view_action = QAction('重置视图(&R)', self)
        reset_view_action.triggered.connect(self.reset_view)
        view_menu.addAction(reset_view_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')
        
        # 关于
        about_action = QAction('关于(&A)', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QToolBar()
        toolbar.setMovable(False)
        self.addToolBar(toolbar)
        
        # 连接/断开按钮
        self.connect_action = QAction('连接', self)
        self.connect_action.triggered.connect(self.toggle_connection)
        toolbar.addAction(self.connect_action)
        
        toolbar.addSeparator()
        
        # 暂停/继续按钮
        self.pause_action = QAction('暂停', self)
        self.pause_action.triggered.connect(self.toggle_pause)
        toolbar.addAction(self.pause_action)
        
        # 清空数据按钮
        clear_action = QAction('清空', self)
        clear_action.triggered.connect(self.clear_data)
        toolbar.addAction(clear_action)
        
        toolbar.addSeparator()
        
        # 记录按钮
        self.record_action = QAction('记录', self)
        self.record_action.triggered.connect(self.toggle_recording)
        toolbar.addAction(self.record_action)
        
        # 导出按钮
        export_action = QAction('导出', self)
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)
    
    def setup_connections(self):
        """设置信号连接"""
        # 控制面板信号
        self.control_panel.connect_requested.connect(self.on_connect_requested)
        self.control_panel.disconnect_requested.connect(self.on_disconnect_requested)
        self.control_panel.recording_toggled.connect(self.on_recording_toggled)
        self.control_panel.export_requested.connect(self.export_data)
        self.control_panel.clear_data_requested.connect(self.clear_data)
        self.control_panel.pause_toggled.connect(self.on_pause_toggled)
        self.control_panel.view_reset_requested.connect(self.reset_view)
        
        # 数据控制器信号
        self.data_controller.new_data_available.connect(self.on_new_data)
        self.data_controller.recording_status_changed.connect(self.on_recording_status_changed)
        self.data_controller.export_completed.connect(self.on_export_completed)
        
        # 串口管理器信号
        self.data_controller.serial_manager.connection_changed.connect(self.on_connection_changed)
        self.data_controller.serial_manager.error_occurred.connect(self.on_serial_error)
        self.data_controller.serial_manager.port_list_updated.connect(self.control_panel.update_port_list)
        
        # 数据解析器信号
        self.data_controller.data_parser.data_statistics.connect(self.control_panel.update_statistics)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 显示初始状态
        self.status_bar.showMessage("就绪")
    
    # 槽函数
    @pyqtSlot(str, int)
    def on_connect_requested(self, port: str, baudrate: int):
        """处理连接请求"""
        if self.data_controller.connect_serial(port, baudrate):
            self.status_bar.showMessage(f"正在连接到 {port}...")
            self.control_panel.add_log_message(f"尝试连接到 {port}, 波特率: {baudrate}")
        else:
            self.status_bar.showMessage("连接失败")
    
    @pyqtSlot()
    def on_disconnect_requested(self):
        """处理断开连接请求"""
        self.data_controller.disconnect_serial()
        self.status_bar.showMessage("已断开连接")
        self.control_panel.add_log_message("串口连接已断开")
    
    @pyqtSlot(bool)
    def on_connection_changed(self, connected: bool):
        """处理连接状态变化"""
        self.control_panel.update_connection_status(connected)
        
        if connected:
            self.status_bar.showMessage("串口已连接")
            self.connect_action.setText("断开")
            self.control_panel.add_log_message("串口连接成功")
        else:
            self.status_bar.showMessage("串口未连接")
            self.connect_action.setText("连接")
            self.control_panel.add_log_message("串口连接断开")
    
    @pyqtSlot(object)
    def on_new_data(self, sensor_data):
        """处理新数据"""
        # 更新3D可视化
        self.opengl_widget.update_sensor_data(sensor_data)
        
        # 更新控制面板显示
        self.control_panel.update_sensor_data(sensor_data)
        
        # 应用渲染选项
        render_options = self.control_panel.get_render_options()
        self.opengl_widget.set_render_options(**render_options)
    
    @pyqtSlot(bool)
    def on_recording_toggled(self, recording: bool):
        """处理记录状态切换"""
        if recording:
            self.data_controller.start_recording()
            self.record_action.setText("停止记录")
            self.control_panel.add_log_message("开始数据记录")
        else:
            self.data_controller.stop_recording()
            self.record_action.setText("记录")
            self.control_panel.add_log_message("停止数据记录")
    
    @pyqtSlot(bool)
    def on_recording_status_changed(self, recording: bool):
        """处理记录状态变化"""
        self.record_action.setText("停止记录" if recording else "记录")
    
    @pyqtSlot(bool)
    def on_pause_toggled(self, paused: bool):
        """处理暂停状态切换"""
        if paused:
            self.data_controller.pause_data_processing()
            self.pause_action.setText("继续")
            self.status_bar.showMessage("数据处理已暂停")
        else:
            self.data_controller.resume_data_processing()
            self.pause_action.setText("暂停")
            self.status_bar.showMessage("数据处理已恢复")
    
    @pyqtSlot(str)
    def on_export_completed(self, filepath: str):
        """处理导出完成"""
        self.status_bar.showMessage(f"数据已导出到: {os.path.basename(filepath)}")
        self.control_panel.add_log_message(f"数据导出完成: {os.path.basename(filepath)}")
        
        # 显示成功消息
        QMessageBox.information(self, "导出完成", f"数据已成功导出到:\n{filepath}")
    
    @pyqtSlot(str)
    def on_serial_error(self, error_msg: str):
        """处理串口错误"""
        self.status_bar.showMessage(f"串口错误: {error_msg}")
        self.control_panel.add_log_message(f"错误: {error_msg}")
    
    # 菜单和工具栏动作
    def toggle_connection(self):
        """切换连接状态"""
        if self.data_controller.is_serial_connected():
            self.on_disconnect_requested()
        else:
            # 使用控制面板的当前设置
            port = self.control_panel.port_combo.currentText()
            baudrate = int(self.control_panel.baud_combo.currentText())
            self.on_connect_requested(port, baudrate)
    
    def toggle_pause(self):
        """切换暂停状态"""
        self.control_panel.on_pause_clicked()
    
    def toggle_recording(self):
        """切换记录状态"""
        self.control_panel.on_record_clicked()
    
    def clear_data(self):
        """清空数据"""
        self.data_controller.clear_data_buffer()
        self.status_bar.showMessage("数据已清空")
        self.control_panel.add_log_message("数据缓冲区已清空")
    
    def export_data(self):
        """导出数据"""
        try:
            # 选择保存文件
            filename, _ = QFileDialog.getSaveFileName(
                self, 
                "导出数据", 
                f"sensor_data_{self.get_timestamp()}.csv",
                "CSV文件 (*.csv)"
            )
            
            if filename:
                # 导出缓冲区数据
                self.data_controller.export_buffer_data(os.path.basename(filename))
                
        except Exception as e:
            QMessageBox.warning(self, "导出失败", f"导出数据时发生错误:\n{str(e)}")
            self.logger.error(f"导出数据失败: {e}")
    
    def reset_view(self):
        """重置3D视图"""
        self.opengl_widget.reset_view()
        self.status_bar.showMessage("视图已重置")
    
    def toggle_fullscreen(self):
        """切换全屏模式"""
        if self.is_fullscreen:
            self.showNormal()
            self.is_fullscreen = False
        else:
            self.showFullScreen()
            self.is_fullscreen = True
    
    def show_about(self):
        """显示关于对话框"""
        about_text = f"""
        <h3>{APP_NAME}</h3>
        <p>版本: {APP_VERSION}</p>
        <p>版权归属: 米醋电子工作室</p>
        <p>一个用于实时显示串口传感器数据的3D可视化软件</p>
        
        <p><b>功能特点:</b></p>
        <ul>
        <li>实时串口数据接收</li>
        <li>3D传感器数据可视化</li>
        <li>数据记录和导出</li>
        <li>多种显示选项</li>
        </ul>
        """
        
        QMessageBox.about(self, "关于", about_text)
    
    def update_status(self):
        """更新状态信息"""
        if self.data_controller.is_serial_connected():
            status = self.data_controller.get_status()
            buffer_size = status.get('buffer_size', 0)
            
            if buffer_size > 0:
                self.status_bar.showMessage(f"已连接 | 缓冲区: {buffer_size} 条数据")
    
    def get_timestamp(self):
        """获取时间戳字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        # 断开串口连接
        if self.data_controller.is_serial_connected():
            self.data_controller.disconnect_serial()
        
        # 保存窗口大小到配置
        self.config.set('DISPLAY', 'window_width', str(self.width()))
        self.config.set('DISPLAY', 'window_height', str(self.height()))
        
        self.logger.info("应用程序正在关闭")
        event.accept()
