(['C:\\Users\\<USER>\\Documents\\augment-projects\\com '
  'assistant\\SerialDataVisualizer_Professional.py'],
 ['C:\\Users\\<USER>\\Documents\\augment-projects\\com assistant'],
 ['tkinter',
  'tkinter.ttk',
  'tkinter.filedialog',
  'tkinter.messagebox',
  'matplotlib',
  'matplotlib.backends.backend_tkagg',
  'matplotlib.figure',
  'matplotlib.animation',
  'mpl_toolkits.mplot3d',
  'numpy',
  'serial',
  'serial.tools.list_ports',
  'csv',
  'configparser',
  'logging',
  'logging.handlers',
  'threading',
  'datetime',
  'argparse'],
 [('D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('README_Professional.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\README_Professional.md',
   'DATA'),
  ('config.ini',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com assistant\\config.ini',
   'DATA'),
  ('docs\\architecture\\Architecture_SerialDataVisualizer_v1.0.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\architecture\\Architecture_SerialDataVisualizer_v1.0.md',
   'DATA'),
  ('docs\\development\\Delivery_Report_v1.0.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\development\\Delivery_Report_v1.0.md',
   'DATA'),
  ('docs\\development\\Optimization_Report_Baudrate_v1.1.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\development\\Optimization_Report_Baudrate_v1.1.md',
   'DATA'),
  ('docs\\development\\Professional_Visualizer_Documentation.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\development\\Professional_Visualizer_Documentation.md',
   'DATA'),
  ('docs\\development\\Project_Completion_Summary.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\development\\Project_Completion_Summary.md',
   'DATA'),
  ('docs\\development\\Technical_Documentation_v1.0.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\development\\Technical_Documentation_v1.0.md',
   'DATA'),
  ('docs\\prd\\PRD_SerialDataVisualizer_v1.0.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\prd\\PRD_SerialDataVisualizer_v1.0.md',
   'DATA'),
  ('docs\\tasks\\TaskPlan_SerialDataVisualizer_v1.0.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\tasks\\TaskPlan_SerialDataVisualizer_v1.0.md',
   'DATA'),
  ('使用说明.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com assistant\\使用说明.md',
   'DATA')],
 '3.13.7 (tags/v3.13.7:bcee1c3, Aug 14 2025, 14:15:11) [MSC v.1944 64 bit '
 '(AMD64)]',
 [('pyi_rth_mplconfig',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('SerialDataVisualizer_Professional',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\SerialDataVisualizer_Professional.py',
   'PYSOURCE')],
 [('zipfile',
   'D:\\Program Files\\Python313\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\Program Files\\Python313\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Program Files\\Python313\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib',
   'D:\\Program Files\\Python313\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._local',
   'D:\\Program Files\\Python313\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Program Files\\Python313\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Program Files\\Python313\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ipaddress', 'D:\\Program Files\\Python313\\Lib\\ipaddress.py', 'PYMODULE'),
  ('glob', 'D:\\Program Files\\Python313\\Lib\\glob.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Program Files\\Python313\\Lib\\fnmatch.py', 'PYMODULE'),
  ('pathlib._abc',
   'D:\\Program Files\\Python313\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Program Files\\Python313\\Lib\\contextlib.py',
   'PYMODULE'),
  ('py_compile',
   'D:\\Program Files\\Python313\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Program Files\\Python313\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Program Files\\Python313\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Program Files\\Python313\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Program Files\\Python313\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Program Files\\Python313\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Program Files\\Python313\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Program Files\\Python313\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Program Files\\Python313\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Program Files\\Python313\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\Program Files\\Python313\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Program Files\\Python313\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Program Files\\Python313\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Program Files\\Python313\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Program Files\\Python313\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('copy', 'D:\\Program Files\\Python313\\Lib\\copy.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Program Files\\Python313\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Program Files\\Python313\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Program Files\\Python313\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\Program Files\\Python313\\Lib\\gettext.py', 'PYMODULE'),
  ('email.charset',
   'D:\\Program Files\\Python313\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Program Files\\Python313\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Program Files\\Python313\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Program Files\\Python313\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Program Files\\Python313\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Program Files\\Python313\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Program Files\\Python313\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('socket', 'D:\\Program Files\\Python313\\Lib\\socket.py', 'PYMODULE'),
  ('selectors', 'D:\\Program Files\\Python313\\Lib\\selectors.py', 'PYMODULE'),
  ('email._parseaddr',
   'D:\\Program Files\\Python313\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\Program Files\\Python313\\Lib\\calendar.py', 'PYMODULE'),
  ('quopri', 'D:\\Program Files\\Python313\\Lib\\quopri.py', 'PYMODULE'),
  ('importlib.abc',
   'D:\\Program Files\\Python313\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Program Files\\Python313\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Program Files\\Python313\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'D:\\Program Files\\Python313\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Program Files\\Python313\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Program Files\\Python313\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile', 'D:\\Program Files\\Python313\\Lib\\tempfile.py', 'PYMODULE'),
  ('importlib._abc',
   'D:\\Program Files\\Python313\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Program Files\\Python313\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('textwrap', 'D:\\Program Files\\Python313\\Lib\\textwrap.py', 'PYMODULE'),
  ('email',
   'D:\\Program Files\\Python313\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Program Files\\Python313\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Program Files\\Python313\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('json', 'D:\\Program Files\\Python313\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\Program Files\\Python313\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Program Files\\Python313\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Program Files\\Python313\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('__future__',
   'D:\\Program Files\\Python313\\Lib\\__future__.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Program Files\\Python313\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Program Files\\Python313\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Program Files\\Python313\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize', 'D:\\Program Files\\Python313\\Lib\\tokenize.py', 'PYMODULE'),
  ('token', 'D:\\Program Files\\Python313\\Lib\\token.py', 'PYMODULE'),
  ('lzma', 'D:\\Program Files\\Python313\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'D:\\Program Files\\Python313\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'D:\\Program Files\\Python313\\Lib\\bz2.py', 'PYMODULE'),
  ('struct', 'D:\\Program Files\\Python313\\Lib\\struct.py', 'PYMODULE'),
  ('shutil', 'D:\\Program Files\\Python313\\Lib\\shutil.py', 'PYMODULE'),
  ('tarfile', 'D:\\Program Files\\Python313\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Program Files\\Python313\\Lib\\gzip.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\Program Files\\Python313\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Program Files\\Python313\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\Program Files\\Python313\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Program Files\\Python313\\Lib\\opcode.py', 'PYMODULE'),
  ('_opcode_metadata',
   'D:\\Program Files\\Python313\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('ast', 'D:\\Program Files\\Python313\\Lib\\ast.py', 'PYMODULE'),
  ('subprocess',
   'D:\\Program Files\\Python313\\Lib\\subprocess.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Program Files\\Python313\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Program Files\\Python313\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Program Files\\Python313\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Program Files\\Python313\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\Program Files\\Python313\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Program Files\\Python313\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Program Files\\Python313\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Program Files\\Python313\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\Program Files\\Python313\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Program Files\\Python313\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Program Files\\Python313\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Program Files\\Python313\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Program Files\\Python313\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\Program Files\\Python313\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http', 'D:\\Program Files\\Python313\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('ssl', 'D:\\Program Files\\Python313\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\Program Files\\Python313\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Program Files\\Python313\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('hashlib', 'D:\\Program Files\\Python313\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'D:\\Program Files\\Python313\\Lib\\bisect.py', 'PYMODULE'),
  ('xml.sax',
   'D:\\Program Files\\Python313\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Program Files\\Python313\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Program Files\\Python313\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Program Files\\Python313\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Program Files\\Python313\\Lib\\http\\client.py',
   'PYMODULE'),
  ('decimal', 'D:\\Program Files\\Python313\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Program Files\\Python313\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Program Files\\Python313\\Lib\\contextvars.py',
   'PYMODULE'),
  ('numbers', 'D:\\Program Files\\Python313\\Lib\\numbers.py', 'PYMODULE'),
  ('hmac', 'D:\\Program Files\\Python313\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\Program Files\\Python313\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Program Files\\Python313\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Program Files\\Python313\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Program Files\\Python313\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Program Files\\Python313\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Program Files\\Python313\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\Program Files\\Python313\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\Program Files\\Python313\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle', 'D:\\Program Files\\Python313\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Program Files\\Python313\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'D:\\Program Files\\Python313\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Program Files\\Python313\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy', 'D:\\Program Files\\Python313\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Program Files\\Python313\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\Program Files\\Python313\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\Program Files\\Python313\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.tempfile',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\tempfile.py',
   'PYMODULE'),
  ('_pyi_rth_utils._win32',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\_win32.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('matplotlib',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\__init__.py',
   'PYMODULE'),
  ('matplotlib.style.core',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\style\\core.py',
   'PYMODULE'),
  ('certifi',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('matplotlib.rcsetup',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\rcsetup.py',
   'PYMODULE'),
  ('cycler',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\cycler\\__init__.py',
   'PYMODULE'),
  ('matplotlib._enums',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\_enums.py',
   'PYMODULE'),
  ('matplotlib._fontconfig_pattern',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_fontconfig_pattern.py',
   'PYMODULE'),
  ('pyparsing',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\pyparsing\\__init__.py',
   'PYMODULE'),
  ('pyparsing.common',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\pyparsing\\common.py',
   'PYMODULE'),
  ('pyparsing.testing',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\pyparsing\\testing.py',
   'PYMODULE'),
  ('pyparsing.unicode',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\pyparsing\\unicode.py',
   'PYMODULE'),
  ('pyparsing.helpers',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\pyparsing\\helpers.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\Program Files\\Python313\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html', 'D:\\Program Files\\Python313\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('pyparsing.results',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\pyparsing\\results.py',
   'PYMODULE'),
  ('pyparsing.core',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\pyparsing\\core.py',
   'PYMODULE'),
  ('pyparsing.diagram',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\pyparsing\\diagram\\__init__.py',
   'PYMODULE'),
  ('jinja2',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\_identifier.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Program Files\\Python313\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Program Files\\Python313\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Program Files\\Python313\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Program Files\\Python313\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Program Files\\Python313\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Program Files\\Python313\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('jinja2.defaults',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\async_utils.py',
   'PYMODULE'),
  ('markupsafe',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\markupsafe\\__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\markupsafe\\_native.py',
   'PYMODULE'),
  ('jinja2.utils',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\jinja2\\nodes.py',
   'PYMODULE'),
  ('pyparsing.actions',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\pyparsing\\actions.py',
   'PYMODULE'),
  ('pyparsing.exceptions',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\pyparsing\\exceptions.py',
   'PYMODULE'),
  ('pyparsing.util',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\pyparsing\\util.py',
   'PYMODULE'),
  ('matplotlib.backends',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backends\\__init__.py',
   'PYMODULE'),
  ('matplotlib.backends._backend_tk',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backends\\_backend_tk.py',
   'PYMODULE'),
  ('matplotlib._pylab_helpers',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_pylab_helpers.py',
   'PYMODULE'),
  ('matplotlib.backend_bases',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backend_bases.py',
   'PYMODULE'),
  ('matplotlib.texmanager',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\texmanager.py',
   'PYMODULE'),
  ('matplotlib.path',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\path.py',
   'PYMODULE'),
  ('matplotlib.hatch',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\hatch.py',
   'PYMODULE'),
  ('matplotlib.bezier',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\bezier.py',
   'PYMODULE'),
  ('matplotlib.layout_engine',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\layout_engine.py',
   'PYMODULE'),
  ('matplotlib._constrained_layout',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_constrained_layout.py',
   'PYMODULE'),
  ('matplotlib._layoutgrid',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_layoutgrid.py',
   'PYMODULE'),
  ('kiwisolver',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\kiwisolver\\__init__.py',
   'PYMODULE'),
  ('kiwisolver.exceptions',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\kiwisolver\\exceptions.py',
   'PYMODULE'),
  ('matplotlib.artist',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\artist.py',
   'PYMODULE'),
  ('matplotlib.backend_managers',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backend_managers.py',
   'PYMODULE'),
  ('matplotlib.widgets',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\widgets.py',
   'PYMODULE'),
  ('matplotlib.ticker',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\ticker.py',
   'PYMODULE'),
  ('matplotlib.transforms',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\transforms.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._core.numeric',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE'),
  ('numpy._core._asarray',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE'),
  ('numpy._core.arrayprint',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE'),
  ('numpy._core.printoptions',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE'),
  ('numpy._core.fromnumeric',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy._core._methods',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._core._exceptions',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE'),
  ('numpy._core._ufunc_config',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy._core.shape_base',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE'),
  ('numpy._core.numerictypes',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE'),
  ('numpy._core._dtype',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE'),
  ('numpy._core._type_aliases',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy._core._string_helpers',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy._core.multiarray',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE'),
  ('numpy._core',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE'),
  ('numpy._core._internal',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE'),
  ('numpy._core._dtype_ctypes',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy._core._add_newdocs',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy._core.einsumfunc',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy._core._machar',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE'),
  ('numpy._core.function_base',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE'),
  ('numpy._core.memmap',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE'),
  ('numpy._core.records',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE'),
  ('numpy._core.getlimits',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib._index_tricks_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.lib._function_base_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._histograms_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE'),
  ('numpy.lib._twodim_base_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE'),
  ('numpy.lib.array_utils',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE'),
  ('numpy.lib._array_utils_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib._arraypad_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE'),
  ('numpy.lib._arrayterator_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE'),
  ('numpy.lib._npyio_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._polynomial_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE'),
  ('numpy.lib._arraysetops_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE'),
  ('numpy.lib._utils_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Program Files\\Python313\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\Program Files\\Python313\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('_ios_support',
   'D:\\Program Files\\Python313\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('shlex', 'D:\\Program Files\\Python313\\Lib\\shlex.py', 'PYMODULE'),
  ('http.server',
   'D:\\Program Files\\Python313\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\Program Files\\Python313\\Lib\\socketserver.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Program Files\\Python313\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\Program Files\\Python313\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('_pyrepl.pager',
   'D:\\Program Files\\Python313\\Lib\\_pyrepl\\pager.py',
   'PYMODULE'),
  ('_pyrepl',
   'D:\\Program Files\\Python313\\Lib\\_pyrepl\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\Program Files\\Python313\\Lib\\tty.py', 'PYMODULE'),
  ('sysconfig',
   'D:\\Program Files\\Python313\\Lib\\sysconfig\\__init__.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\Program Files\\Python313\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('platform', 'D:\\Program Files\\Python313\\Lib\\platform.py', 'PYMODULE'),
  ('numpy.lib._ufunclike_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE'),
  ('numpy.lib._shape_base_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE'),
  ('numpy.lib._type_check_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib._scimath_impl',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.introspect',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE'),
  ('numpy._core.umath',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE'),
  ('numpy._core.overrides',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('doctest', 'D:\\Program Files\\Python313\\Lib\\doctest.py', 'PYMODULE'),
  ('_colorize', 'D:\\Program Files\\Python313\\Lib\\_colorize.py', 'PYMODULE'),
  ('pdb', 'D:\\Program Files\\Python313\\Lib\\pdb.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\Program Files\\Python313\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('codeop', 'D:\\Program Files\\Python313\\Lib\\codeop.py', 'PYMODULE'),
  ('code', 'D:\\Program Files\\Python313\\Lib\\code.py', 'PYMODULE'),
  ('bdb', 'D:\\Program Files\\Python313\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\Program Files\\Python313\\Lib\\cmd.py', 'PYMODULE'),
  ('difflib', 'D:\\Program Files\\Python313\\Lib\\difflib.py', 'PYMODULE'),
  ('numpy._core.tests._natype',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\tests\\_natype.py',
   'PYMODULE'),
  ('numpy._core.tests', '-', 'PYMODULE'),
  ('unittest.case',
   'D:\\Program Files\\Python313\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Program Files\\Python313\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Program Files\\Python313\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Program Files\\Python313\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Program Files\\Python313\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Program Files\\Python313\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Program Files\\Python313\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Program Files\\Python313\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Program Files\\Python313\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Program Files\\Python313\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Program Files\\Python313\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.linalg._linalg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._ufunc',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nbit_base',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('matplotlib.text',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\text.py',
   'PYMODULE'),
  ('matplotlib.patheffects',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\patheffects.py',
   'PYMODULE'),
  ('matplotlib.textpath',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\textpath.py',
   'PYMODULE'),
  ('matplotlib.mathtext',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\mathtext.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'D:\\Program Files\\Python313\\Lib\\colorsys.py', 'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('fractions', 'D:\\Program Files\\Python313\\Lib\\fractions.py', 'PYMODULE'),
  ('PIL._util',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Program Files\\Python313\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Program Files\\Python313\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Program Files\\Python313\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Program Files\\Python313\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Program Files\\Python313\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\Program Files\\Python313\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.font',
   'D:\\Program Files\\Python313\\Lib\\tkinter\\font.py',
   'PYMODULE'),
  ('uuid', 'D:\\Program Files\\Python313\\Lib\\uuid.py', 'PYMODULE'),
  ('matplotlib.backends.backend_webagg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_webagg_core',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backends\\backend_webagg_core.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_agg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backends\\backend_agg.py',
   'PYMODULE'),
  ('matplotlib.backends.registry',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backends\\registry.py',
   'PYMODULE'),
  ('matplotlib._tight_bbox',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_tight_bbox.py',
   'PYMODULE'),
  ('matplotlib.colors',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\colors.py',
   'PYMODULE'),
  ('matplotlib._color_data',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_color_data.py',
   'PYMODULE'),
  ('matplotlib.font_manager',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\font_manager.py',
   'PYMODULE'),
  ('plistlib', 'D:\\Program Files\\Python313\\Lib\\plistlib.py', 'PYMODULE'),
  ('matplotlib.style',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\style\\__init__.py',
   'PYMODULE'),
  ('matplotlib.collections',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\collections.py',
   'PYMODULE'),
  ('matplotlib.projections',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\projections\\__init__.py',
   'PYMODULE'),
  ('matplotlib.projections.polar',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\projections\\polar.py',
   'PYMODULE'),
  ('matplotlib.spines',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\spines.py',
   'PYMODULE'),
  ('matplotlib.markers',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\markers.py',
   'PYMODULE'),
  ('matplotlib.axis',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\axis.py',
   'PYMODULE'),
  ('dateutil.tz',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE'),
  ('dateutil._common',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE'),
  ('dateutil.parser',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE'),
  ('six',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('dateutil',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE'),
  ('matplotlib.units',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\units.py',
   'PYMODULE'),
  ('matplotlib.projections.geo',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\projections\\geo.py',
   'PYMODULE'),
  ('matplotlib.axes',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\axes\\__init__.py',
   'PYMODULE'),
  ('matplotlib.axes._axes',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\axes\\_axes.py',
   'PYMODULE'),
  ('matplotlib.container',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\container.py',
   'PYMODULE'),
  ('matplotlib.axes._secondary_axes',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\axes\\_secondary_axes.py',
   'PYMODULE'),
  ('matplotlib.tri',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\tri\\__init__.py',
   'PYMODULE'),
  ('matplotlib.tri._tritools',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_tritools.py',
   'PYMODULE'),
  ('matplotlib.tri._trirefine',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_trirefine.py',
   'PYMODULE'),
  ('matplotlib.tri._triplot',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_triplot.py',
   'PYMODULE'),
  ('matplotlib.tri._tripcolor',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_tripcolor.py',
   'PYMODULE'),
  ('matplotlib.tri._triinterpolate',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_triinterpolate.py',
   'PYMODULE'),
  ('matplotlib.tri._trifinder',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_trifinder.py',
   'PYMODULE'),
  ('matplotlib.tri._tricontour',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_tricontour.py',
   'PYMODULE'),
  ('matplotlib.tri._triangulation',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\tri\\_triangulation.py',
   'PYMODULE'),
  ('matplotlib.table',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\table.py',
   'PYMODULE'),
  ('matplotlib.streamplot',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\streamplot.py',
   'PYMODULE'),
  ('matplotlib.stackplot',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\stackplot.py',
   'PYMODULE'),
  ('matplotlib.quiver',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\quiver.py',
   'PYMODULE'),
  ('matplotlib.mlab',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\mlab.py',
   'PYMODULE'),
  ('matplotlib.legend',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\legend.py',
   'PYMODULE'),
  ('matplotlib.inset',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\inset.py',
   'PYMODULE'),
  ('matplotlib.image',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\image.py',
   'PYMODULE'),
  ('matplotlib.dates',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\dates.py',
   'PYMODULE'),
  ('matplotlib.contour',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\contour.py',
   'PYMODULE'),
  ('contourpy',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\contourpy\\__init__.py',
   'PYMODULE'),
  ('contourpy.enum_util',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\contourpy\\enum_util.py',
   'PYMODULE'),
  ('contourpy.dechunk',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\contourpy\\dechunk.py',
   'PYMODULE'),
  ('contourpy.typecheck',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\contourpy\\typecheck.py',
   'PYMODULE'),
  ('contourpy.types',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\contourpy\\types.py',
   'PYMODULE'),
  ('contourpy.array',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\contourpy\\array.py',
   'PYMODULE'),
  ('contourpy.convert',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\contourpy\\convert.py',
   'PYMODULE'),
  ('contourpy.chunk',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\contourpy\\chunk.py',
   'PYMODULE'),
  ('contourpy._version',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\contourpy\\_version.py',
   'PYMODULE'),
  ('matplotlib.category',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\category.py',
   'PYMODULE'),
  ('matplotlib.axes._base',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\axes\\_base.py',
   'PYMODULE'),
  ('matplotlib.gridspec',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\gridspec.py',
   'PYMODULE'),
  ('matplotlib.legend_handler',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\legend_handler.py',
   'PYMODULE'),
  ('matplotlib._tight_layout',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_tight_layout.py',
   'PYMODULE'),
  ('matplotlib.offsetbox',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\offsetbox.py',
   'PYMODULE'),
  ('matplotlib._blocking_input',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_blocking_input.py',
   'PYMODULE'),
  ('matplotlib._mathtext',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\_mathtext.py',
   'PYMODULE'),
  ('matplotlib._mathtext_data',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_mathtext_data.py',
   'PYMODULE'),
  ('matplotlib.dviread',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\dviread.py',
   'PYMODULE'),
  ('matplotlib._text_helpers',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_text_helpers.py',
   'PYMODULE'),
  ('matplotlib.patches',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\patches.py',
   'PYMODULE'),
  ('matplotlib.lines',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\lines.py',
   'PYMODULE'),
  ('matplotlib.colorizer',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\colorizer.py',
   'PYMODULE'),
  ('matplotlib.cm',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\cm.py',
   'PYMODULE'),
  ('matplotlib._cm_bivar',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\_cm_bivar.py',
   'PYMODULE'),
  ('matplotlib._cm_multivar',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_cm_multivar.py',
   'PYMODULE'),
  ('matplotlib._cm_listed',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_cm_listed.py',
   'PYMODULE'),
  ('matplotlib.scale',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\scale.py',
   'PYMODULE'),
  ('matplotlib._afm',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\_afm.py',
   'PYMODULE'),
  ('matplotlib._docstring',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_docstring.py',
   'PYMODULE'),
  ('matplotlib._cm',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\_cm.py',
   'PYMODULE'),
  ('matplotlib.backend_tools',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backend_tools.py',
   'PYMODULE'),
  ('matplotlib.cbook',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\cbook.py',
   'PYMODULE'),
  ('matplotlib._version',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\_version.py',
   'PYMODULE'),
  ('matplotlib._api',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_api\\__init__.py',
   'PYMODULE'),
  ('matplotlib._api.deprecation',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_api\\deprecation.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Program Files\\Python313\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Program Files\\Python313\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Program Files\\Python313\\Lib\\stringprep.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\Program Files\\Python313\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\Program Files\\Python313\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\Program Files\\Python313\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('random', 'D:\\Program Files\\Python313\\Lib\\random.py', 'PYMODULE'),
  ('statistics',
   'D:\\Program Files\\Python313\\Lib\\statistics.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\Program Files\\Python313\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('smtplib', 'D:\\Program Files\\Python313\\Lib\\smtplib.py', 'PYMODULE'),
  ('logging',
   'D:\\Program Files\\Python313\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Program Files\\Python313\\Lib\\configparser.py',
   'PYMODULE'),
  ('csv', 'D:\\Program Files\\Python313\\Lib\\csv.py', 'PYMODULE'),
  ('serial.tools.list_ports',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE'),
  ('serial.tools',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE'),
  ('serial.tools.list_ports_common',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE'),
  ('serial.tools.list_ports_posix',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE'),
  ('serial.tools.list_ports_osx',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE'),
  ('serial.tools.list_ports_linux',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE'),
  ('serial.tools.list_ports_windows',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE'),
  ('serial.win32',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\serial\\win32.py',
   'PYMODULE'),
  ('serial',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\serial\\__init__.py',
   'PYMODULE'),
  ('serial.serialjava',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE'),
  ('serial.serialposix',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE'),
  ('serial.serialwin32',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE'),
  ('serial.serialcli',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE'),
  ('serial.serialutil',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE'),
  ('numpy',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.strings',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE'),
  ('numpy._core.strings',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._utils',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE'),
  ('numpy.char',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE'),
  ('numpy._core.defchararray',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE'),
  ('numpy.rec',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py.diagnose',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE'),
  ('numpy.f2py.f2py2e',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE'),
  ('numpy.f2py._backends',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE'),
  ('numpy.f2py._backends._distutils',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE'),
  ('numpy.f2py._backends._backend',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE'),
  ('numpy.f2py._backends._meson',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE'),
  ('numpy.f2py.auxfuncs',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.f90mod_rules',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE'),
  ('numpy.f2py.rules',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE'),
  ('numpy.f2py.use_rules',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE'),
  ('numpy.f2py.common_rules',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE'),
  ('numpy.f2py.func2subr',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE'),
  ('numpy.f2py._isocbind',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE'),
  ('numpy.f2py.crackfortran',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE'),
  ('charset_normalizer',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('fileinput', 'D:\\Program Files\\Python313\\Lib\\fileinput.py', 'PYMODULE'),
  ('numpy.f2py.symbolic',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE'),
  ('numpy.f2py.cb_rules',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE'),
  ('numpy.f2py.capi_maps',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE'),
  ('numpy.f2py.cfuncs',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE'),
  ('numpy.f2py.__version__',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE'),
  ('numpy.matlib',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._helper',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy._array_api_info',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('yaml',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._expired_attrs_2_0',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\__init__.py',
   'PYMODULE'),
  ('mpl_toolkits', '-', 'PYMODULE'),
  ('mpl_toolkits.mplot3d.axes3d',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axes3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.axis3d',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\axis3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.art3d',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\art3d.py',
   'PYMODULE'),
  ('mpl_toolkits.mplot3d.proj3d',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\proj3d.py',
   'PYMODULE'),
  ('matplotlib.animation',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\animation.py',
   'PYMODULE'),
  ('matplotlib._animation_data',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_animation_data.py',
   'PYMODULE'),
  ('matplotlib.figure',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\figure.py',
   'PYMODULE'),
  ('matplotlib.colorbar',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\colorbar.py',
   'PYMODULE'),
  ('matplotlib.backends.backend_tkagg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backends\\backend_tkagg.py',
   'PYMODULE'),
  ('matplotlib.pyplot',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\pyplot.py',
   'PYMODULE'),
  ('matplotlib.typing',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\matplotlib\\typing.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\Program Files\\Python313\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\Program Files\\Python313\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\Program Files\\Python313\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Program Files\\Python313\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('typing', 'D:\\Program Files\\Python313\\Lib\\typing.py', 'PYMODULE'),
  ('datetime', 'D:\\Program Files\\Python313\\Lib\\datetime.py', 'PYMODULE'),
  ('_pydatetime',
   'D:\\Program Files\\Python313\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_strptime', 'D:\\Program Files\\Python313\\Lib\\_strptime.py', 'PYMODULE'),
  ('argparse', 'D:\\Program Files\\Python313\\Lib\\argparse.py', 'PYMODULE'),
  ('signal', 'D:\\Program Files\\Python313\\Lib\\signal.py', 'PYMODULE'),
  ('threading', 'D:\\Program Files\\Python313\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Program Files\\Python313\\Lib\\_threading_local.py',
   'PYMODULE')],
 [('python313.dll', 'D:\\Program Files\\Python313\\python313.dll', 'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'D:\\Program Files\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\Program Files\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'D:\\Program Files\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'D:\\Program Files\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Program Files\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Program Files\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Program Files\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Program Files\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Program Files\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Program Files\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Program Files\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd', 'D:\\Program Files\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Program Files\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Program Files\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('kiwisolver\\_cext.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\kiwisolver\\_cext.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'D:\\Program Files\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\_imagingtk.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\_webp.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\_avif.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\_imagingcms.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\_imagingmath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Program Files\\Python313\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\PIL\\_imaging.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'D:\\Program Files\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('matplotlib\\backends\\_tkagg.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backends\\_tkagg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\backends\\_backend_agg.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('contourpy\\_contourpy.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\contourpy\\_contourpy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_tri.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_tri.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_qhull.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_qhull.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_image.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_image.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\ft2font.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\ft2font.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_path.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_path.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\_c_internal_utils.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\mtrand.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\_sfc64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\_philox.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\_pcg64.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\_mt19937.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\bit_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\_generator.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\random\\_common.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('yaml\\_yaml.cp313-win_amd64.pyd',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\yaml\\_yaml.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_tkinter.pyd',
   'D:\\Program Files\\Python313\\DLLs\\_tkinter.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\Program Files\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\Program Files\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'D:\\Program Files\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'D:\\Program Files\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'D:\\Program Files\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Program Files\\Python313\\python3.dll', 'BINARY'),
  ('tcl86t.dll', 'D:\\Program Files\\Python313\\DLLs\\tcl86t.dll', 'BINARY'),
  ('tk86t.dll', 'D:\\Program Files\\Python313\\DLLs\\tk86t.dll', 'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\ucrtbase.dll',
   'BINARY'),
  ('zlib1.dll', 'D:\\Program Files\\Python313\\DLLs\\zlib1.dll', 'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance '
   'Toolkit\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY')],
 [],
 [],
 [('README_Professional.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\README_Professional.md',
   'DATA'),
  ('config.ini',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com assistant\\config.ini',
   'DATA'),
  ('docs\\architecture\\Architecture_SerialDataVisualizer_v1.0.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\architecture\\Architecture_SerialDataVisualizer_v1.0.md',
   'DATA'),
  ('docs\\development\\Delivery_Report_v1.0.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\development\\Delivery_Report_v1.0.md',
   'DATA'),
  ('docs\\development\\Optimization_Report_Baudrate_v1.1.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\development\\Optimization_Report_Baudrate_v1.1.md',
   'DATA'),
  ('docs\\development\\Professional_Visualizer_Documentation.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\development\\Professional_Visualizer_Documentation.md',
   'DATA'),
  ('docs\\development\\Project_Completion_Summary.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\development\\Project_Completion_Summary.md',
   'DATA'),
  ('docs\\development\\Technical_Documentation_v1.0.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\development\\Technical_Documentation_v1.0.md',
   'DATA'),
  ('docs\\prd\\PRD_SerialDataVisualizer_v1.0.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\prd\\PRD_SerialDataVisualizer_v1.0.md',
   'DATA'),
  ('docs\\tasks\\TaskPlan_SerialDataVisualizer_v1.0.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\docs\\tasks\\TaskPlan_SerialDataVisualizer_v1.0.md',
   'DATA'),
  ('使用说明.md',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com assistant\\使用说明.md',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_DEJAVU',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmmi10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Symbol.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery-nogrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help_large.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_mpl-gallery.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmmi10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-pastel.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\classic.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\ggplot.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmsy10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Minduka_Present_Blue_Pack.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvlo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmss10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-talk.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneral.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.pdf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizOneSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Italic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvr8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\grayscale.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-poster.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.pdf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave_large.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\README.txt',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\README.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\Stocks.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmtt10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.pdf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fast.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back_large.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-Bold.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-BoldItalic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagdo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-bright.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\filesave.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\filesave.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\topobathy.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmr10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\axes_grid\\bivariate_normal.npy',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmex10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\jacksboro_fault_dem.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-colorblind.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\eeg.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagk8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.pdf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-white.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFiveSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\goog.npz',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\goog.npz',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-paper.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-dark-palette.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\psyr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzdr.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\embedding_in_wx3.xrc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkli8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmex10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSans.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Oblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizFourSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move_large.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home_large.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Roman.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-Oblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\s1045.ima.gz',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvbo8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-Bold.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\move.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\move.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUniBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\logo2.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\logo2.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\msft.csv',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\msft.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\petroff10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward_large.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\membrane.dat',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\LICENSE_STIX',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.pdf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Helvetica-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\_classic_test_patch.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\fivethirtyeight.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncbi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\kpsewhich.lua',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\kpsewhich.lua',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\plot_directive\\plot_directive.css',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\home.pdf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\home.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\ZapfDingbats.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmb10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\back.pdf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\back.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif-BoldItalic.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-whitegrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.pdf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmr10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Times-Italic.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplb8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.png',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\qt4_editor_options.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\grace_hopper.jpg',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pcrr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvb8an.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\phvro8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pzcmi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizThreeSymBol.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\matplotlib.pdf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\dark_background.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\hand.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\hand.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-deep.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkl8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\ptmri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXGeneralBolIta.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\readme.txt',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkdi8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pbkd8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect.png',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-muted.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-ticks.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\cmsy10.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXSizTwoSymReg.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-notebook.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\tableau-colorblind10.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\putr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\forward.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\forward.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\bmh.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\sample_data\\data_x_x2_x3.csv',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\help-symbolic.svg',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\subplots_large.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\subplots_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSansMono-BoldOblique.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\Solarize_Light2.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\stylelib\\seaborn-v0_8-darkgrid.mplstyle',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\pdfcorefonts\\Courier-BoldOblique.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerifDisplay.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pagko8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\images\\zoom_to_rect_large.png',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\DejaVuSerif.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\cmtt10.ttf',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pncri8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\matplotlibrc',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\matplotlibrc',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\afm\\pplr8a.afm',
   'DATA'),
  ('matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\matplotlib\\mpl-data\\fonts\\ttf\\STIXNonUni.ttf',
   'DATA'),
  ('certifi\\py.typed',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'D:\\Program Files\\Python313\\Lib\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-10',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-10',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kampala',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kampala',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lubumbashi',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lubumbashi',
   'DATA'),
  ('_tcl_data\\msgs\\ca.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\East',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\East',
   'DATA'),
  ('_tcl_data\\msgs\\bn.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\bn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Srednekolymsk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Srednekolymsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Windhoek',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Windhoek',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Freetown',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Freetown',
   'DATA'),
  ('_tcl_data\\encoding\\cp1251.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp1251.enc',
   'DATA'),
  ('_tk_data\\ttk\\sizegrip.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\sizegrip.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\El_Salvador',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\El_Salvador',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+5',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+5',
   'DATA'),
  ('_tk_data\\listbox.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\listbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Nicosia',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vladivostok',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Vladivostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+4',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+4',
   'DATA'),
  ('_tcl_data\\msgs\\pl.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Saratov',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Saratov',
   'DATA'),
  ('_tcl_data\\msgs\\sh.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\sh.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-5.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Makassar',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Makassar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Dawson',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Adak',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Adak',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Indiana-Starke',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Indiana-Starke',
   'DATA'),
  ('_tcl_data\\msgs\\nb.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\nb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Astrakhan',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Astrakhan',
   'DATA'),
  ('_tcl_data\\msgs\\eu_es.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\eu_es.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Turkey',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Turkey',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santarem',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santarem',
   'DATA'),
  ('_tcl_data\\encoding\\cns11643.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cns11643.enc',
   'DATA'),
  ('_tk_data\\entry.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\entry.tcl',
   'DATA'),
  ('_tk_data\\safetk.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\safetk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Goose_Bay',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Goose_Bay',
   'DATA'),
  ('_tk_data\\choosedir.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\choosedir.tcl',
   'DATA'),
  ('_tk_data\\images\\logo.eps',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\logo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indianapolis',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Douala',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Douala',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Christmas',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Christmas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Chisinau',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Chisinau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Easter',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Easter',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Khartoum',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Khartoum',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Guernsey',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Guernsey',
   'DATA'),
  ('_tcl_data\\msgs\\en_sg.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_sg.msg',
   'DATA'),
  ('_tk_data\\ttk\\treeview.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\treeview.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\mt.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\mt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Antananarivo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Antananarivo',
   'DATA'),
  ('_tcl_data\\tzdata\\GB',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\GB',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Berlin',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Berlin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Vincent',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Vincent',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Tasmania',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Tasmania',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\East-Indiana',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\East-Indiana',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dar_es_Salaam',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\London',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\London',
   'DATA'),
  ('_tk_data\\scale.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Kerguelen',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Kerguelen',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-1',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-1',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belgrade',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Belgrade',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hong_Kong',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hong_Kong',
   'DATA'),
  ('_tcl_data\\msgs\\es_ar.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_ar.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Toronto',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Toronto',
   'DATA'),
  ('_tcl_data\\msgs\\en_gb.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Addis_Ababa',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Addis_Ababa',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-2.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-2.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Monticello',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Monticello',
   'DATA'),
  ('_tcl_data\\encoding\\macCentEuro.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macCentEuro.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Israel',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Israel',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Metlakatla',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Metlakatla',
   'DATA'),
  ('_tk_data\\dialog.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\dialog.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dubai',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dubai',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Iqaluit',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Iqaluit',
   'DATA'),
  ('_tcl_data\\encoding\\macJapan.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macJapan.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Enderbury',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Enderbury',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yekaterinburg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yekaterinburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hovd',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hovd',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Midway',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Midway',
   'DATA'),
  ('_tcl_data\\tzdata\\Japan',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Japan',
   'DATA'),
  ('_tcl_data\\tzdata\\WET',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\WET',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Maceio',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Maceio',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Buenos_Aires',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Nicosia',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Nicosia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Marengo',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Marengo',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kosrae',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kosrae',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\LHI',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\LHI',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Rarotonga',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Rarotonga',
   'DATA'),
  ('_tcl_data\\tzdata\\ROK',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\ROK',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mazatlan',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Mazatlan',
   'DATA'),
  ('_tcl_data\\msgs\\af.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\af.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ru.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\encoding\\ascii.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\ascii.enc',
   'DATA'),
  ('_tk_data\\spinbox.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_ca.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_ca.msg',
   'DATA'),
  ('_tk_data\\msgs\\es.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\hi_in.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\hi_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\it_ch.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\it_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Buenos_Aires',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('_tcl_data\\history.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\history.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_sv.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\South',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\South',
   'DATA'),
  ('_tcl_data\\encoding\\cp866.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp866.enc',
   'DATA'),
  ('_tk_data\\obsolete.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\obsolete.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Juba',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Juba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Barbados',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Barbados',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Paris',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Paris',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Shiprock',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Shiprock',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guayaquil',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guayaquil',
   'DATA'),
  ('_tcl_data\\msgs\\es_py.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_py.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-9.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-9.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Danmarkshavn',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Danmarkshavn',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vaduz',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vaduz',
   'DATA'),
  ('_tk_data\\ttk\\menubutton.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\menubutton.tcl',
   'DATA'),
  ('_tk_data\\msgs\\de.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wallis',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Wallis',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vilnius',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vilnius',
   'DATA'),
  ('_tcl_data\\encoding\\cp864.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp864.enc',
   'DATA'),
  ('_tk_data\\msgs\\hu.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\msgs\\be.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\be.msg',
   'DATA'),
  ('_tcl_data\\msgs\\eu.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\eu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Gambier',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Gambier',
   'DATA'),
  ('_tcl_data\\encoding\\cp863.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp863.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp1254.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp1254.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ust-Nera',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ust-Nera',
   'DATA'),
  ('_tcl_data\\msgs\\uk.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\uk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Helsinki',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Helsinki',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Choibalsan',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Choibalsan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Saigon',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Saigon',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Luxembourg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Luxembourg',
   'DATA'),
  ('_tcl_data\\msgs\\id_id.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\id_id.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UCT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\UCT',
   'DATA'),
  ('_tcl_data\\msgs\\en_ph.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_ph.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zaporozhye',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zaporozhye',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Velho',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Porto_Velho',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yangon',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yangon',
   'DATA'),
  ('_tk_data\\clrpick.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\clrpick.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Barnaul',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Barnaul',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Beulah',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Central',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Central',
   'DATA'),
  ('_tcl_data\\msgs\\te_in.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\te_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\mr.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\mr.msg',
   'DATA'),
  ('_tcl_data\\encoding\\euc-kr.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\euc-kr.enc',
   'DATA'),
  ('_tk_data\\ttk\\progress.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\progress.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Greenwich',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Casey',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Casey',
   'DATA'),
  ('_tcl_data\\tzdata\\Universal',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Sao_Tome',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Sao_Tome',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mayotte',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mayotte',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montevideo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Montevideo',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso2022.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Marigot',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Marigot',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Majuro',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Majuro',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maseru',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Maseru',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cambridge_Bay',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cambridge_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Winnipeg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Winnipeg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yakutat',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Yakutat',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lome',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lome',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vatican',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vatican',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Aleutian',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Aleutian',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Macquarie',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Macquarie',
   'DATA'),
  ('_tcl_data\\msgs\\ar_in.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ar_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Eire',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Eire',
   'DATA'),
  ('_tk_data\\images\\README',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\README',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chihuahua',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Chihuahua',
   'DATA'),
  ('_tcl_data\\encoding\\euc-jp.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\euc-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulaanbaatar',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ulaanbaatar',
   'DATA'),
  ('_tcl_data\\msgs\\fo_fo.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\fo_fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Phnom_Penh',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Phnom_Penh',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dushanbe',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dushanbe',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo.eps',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\pwrdLogo.eps',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Mountain',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Mountain',
   'DATA'),
  ('_tk_data\\ttk\\utils.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\utils.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thunder_Bay',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Thunder_Bay',
   'DATA'),
  ('_tcl_data\\msgs\\ar_jo.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ar_jo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Atyrau',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Atyrau',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jayapura',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jayapura',
   'DATA'),
  ('_tk_data\\msgs\\ru.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\ru.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Edmonton',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Edmonton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Famagusta',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Famagusta',
   'DATA'),
  ('_tcl_data\\tzdata\\Cuba',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Cuba',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Truk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Truk',
   'DATA'),
  ('_tcl_data\\encoding\\cp857.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp857.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bogota',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Bogota',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baghdad',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Baghdad',
   'DATA'),
  ('_tcl_data\\encoding\\cp862.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp862.enc',
   'DATA'),
  ('_tcl_data\\msgs\\gl_es.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\gl_es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sq.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\sq.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Indianapolis',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Eastern',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Louisville',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\W-SU',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\W-SU',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anguilla',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Anguilla',
   'DATA'),
  ('_tcl_data\\msgs\\da.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Krasnoyarsk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Krasnoyarsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Yancowinna',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Yancowinna',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaSur',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaSur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Detroit',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Detroit',
   'DATA'),
  ('_tcl_data\\tzdata\\CET',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\CET',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Hawaii',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Hawaii',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6CDT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rosario',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rosario',
   'DATA'),
  ('_tcl_data\\encoding\\cp850.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp850.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Omsk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Omsk',
   'DATA'),
  ('_tk_data\\msgs\\en.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\en.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guadalcanal',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Guadalcanal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kathmandu',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kathmandu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Araguaina',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Araguaina',
   'DATA'),
  ('_tk_data\\msgs\\da.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\da.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Madrid',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Madrid',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Thomas',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Thomas',
   'DATA'),
  ('_tcl_data\\msgs\\kl_gl.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\kl_gl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mendoza',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Mendoza',
   'DATA'),
  ('_tcl_data\\msgs\\es_co.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_co.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Martinique',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Martinique',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\UTC',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bratislava',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Bratislava',
   'DATA'),
  ('_tcl_data\\encoding\\gb1988.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\gb1988.enc',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-1.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-1.enc',
   'DATA'),
  ('_tk_data\\ttk\\combobox.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\combobox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ho_Chi_Minh',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('_tcl_data\\msgs\\zh_sg.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\zh_sg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST5EDT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kigali',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kigali',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pontianak',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Pontianak',
   'DATA'),
  ('_tcl_data\\msgs\\es.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es.msg',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ch.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\fr_ch.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\BajaNorte',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Mexico\\BajaNorte',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Honolulu',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Honolulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Brisbane',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Brisbane',
   'DATA'),
  ('_tcl_data\\encoding\\macGreek.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macGreek.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yerevan',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yerevan',
   'DATA'),
  ('_tk_data\\focus.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\focus.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Libreville',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Libreville',
   'DATA'),
  ('_tcl_data\\msgs\\sv.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Zulu',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kyiv',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kyiv',
   'DATA'),
  ('_tcl_data\\tzdata\\Singapore',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Singapore',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtobe',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtobe',
   'DATA'),
  ('_tcl_data\\encoding\\macDingbats.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macDingbats.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp874.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp874.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\North',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\North',
   'DATA'),
  ('_tcl_data\\msgs\\zh_cn.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tcl_data\\tclIndex',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Skopje',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Skopje',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rio_Branco',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rio_Branco',
   'DATA'),
  ('_tcl_data\\tzdata\\MST7MDT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Hermosillo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Hermosillo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Managua',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Managua',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mahe',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mahe',
   'DATA'),
  ('_tcl_data\\msgs\\en_au.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_au.msg',
   'DATA'),
  ('_tcl_data\\msgs\\en_ie.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Canary',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Canary',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Noronha',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Noronha',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Knox_IN',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Knox_IN',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+8',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+8',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Johns',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Johns',
   'DATA'),
  ('_tcl_data\\msgs\\af_za.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\af_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nuuk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nuuk',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Nauru',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Nauru',
   'DATA'),
  ('_tcl_data\\tzdata\\CST6CDT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\CST6CDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Catamarca',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Catamarca',
   'DATA'),
  ('_tcl_data\\msgs\\es_ni.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_ni.msg',
   'DATA'),
  ('_tcl_data\\encoding\\macIceland.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macIceland.enc',
   'DATA'),
  ('_tcl_data\\msgs\\fr_be.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\fr_be.msg',
   'DATA'),
  ('_tk_data\\msgs\\pt.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Chicago',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Chicago',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9',
   'DATA'),
  ('tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'D:\\Program Files\\Python313\\tcl\\tcl8\\8.4\\platform\\shell-1.1.4.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Maputo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Maputo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atka',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Atka',
   'DATA'),
  ('_tcl_data\\msgs\\en_za.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_za.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-12',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-12',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kabul',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kabul',
   'DATA'),
  ('_tcl_data\\msgs\\kw_gb.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\kw_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tokyo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tokyo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Manaus',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Manaus',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Currie',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Currie',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port-au-Prince',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Port-au-Prince',
   'DATA'),
  ('_tcl_data\\msgs\\ko_kr.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ko_kr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Hongkong',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Hongkong',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\Center',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\Center',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kwajalein',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kwajalein',
   'DATA'),
  ('_tcl_data\\msgs\\fr.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Monterrey',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Monterrey',
   'DATA'),
  ('_tk_data\\msgs\\en_gb.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\en_gb.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Creston',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Creston',
   'DATA'),
  ('_tcl_data\\encoding\\cp860.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp860.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tallinn',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tallinn',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4',
   'DATA'),
  ('_tcl_data\\parray.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\parray.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Auckland',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Auckland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashkhabad',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ashkhabad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nome',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nome',
   'DATA'),
  ('_tcl_data\\msgs\\sr.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\sr.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ta_in.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ta_in.msg',
   'DATA'),
  ('_tcl_data\\msgs\\sl.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\sl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tirane',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tirane',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Victoria',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Victoria',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Johannesburg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Johannesburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Guam',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Guam',
   'DATA'),
  ('_tcl_data\\encoding\\macUkraine.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macUkraine.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Tell_City',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Tell_City',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faeroe',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faeroe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cuiaba',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cuiaba',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Amsterdam',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Amsterdam',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Pyongyang',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Pyongyang',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Vostok',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Vostok',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bujumbura',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bujumbura',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tripoli',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Tripoli',
   'DATA'),
  ('_tcl_data\\encoding\\cp1258.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp1258.enc',
   'DATA'),
  ('tcl8\\8.5\\tcltest-2.5.8.tm',
   'D:\\Program Files\\Python313\\tcl\\tcl8\\8.5\\tcltest-2.5.8.tm',
   'DATA'),
  ('_tcl_data\\encoding\\big5.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\big5.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Porto-Novo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Porto-Novo',
   'DATA'),
  ('_tcl_data\\msgs\\mr_in.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\mr_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Saipan',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Saipan',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-11',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-11',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Jujuy',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Jujuy',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmera',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Asmera',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Whitehorse',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Whitehorse',
   'DATA'),
  ('_tcl_data\\msgs\\fi.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tk_data\\ttk\\clamTheme.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\clamTheme.tcl',
   'DATA'),
  ('_tk_data\\license.terms',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\license.terms',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-5',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-5',
   'DATA'),
  ('_tk_data\\ttk\\winTheme.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\winTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Phoenix',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Phoenix',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Istanbul',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qostanay',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qostanay',
   'DATA'),
  ('_tcl_data\\msgs\\mk.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\mk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Mendoza',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Mendoza',
   'DATA'),
  ('_tcl_data\\tzdata\\EET',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\EET',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bamako',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bamako',
   'DATA'),
  ('_tk_data\\tearoff.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\tearoff.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Coral_Harbour',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Coral_Harbour',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nairobi',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Nairobi',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Athens',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Athens',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Merida',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Merida',
   'DATA'),
  ('_tcl_data\\msgs\\bn_in.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\bn_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Petersburg',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Petersburg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lord_Howe',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Lord_Howe',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novosibirsk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Novosibirsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Copenhagen',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Copenhagen',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Taipei',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Taipei',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Volgograd',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Volgograd',
   'DATA'),
  ('_tk_data\\palette.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\palette.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_lb.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ar_lb.msg',
   'DATA'),
  ('_tcl_data\\clock.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\clock.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aden',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aden',
   'DATA'),
  ('_tcl_data\\tzdata\\Iran',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Iran',
   'DATA'),
  ('_tk_data\\console.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\console.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Manila',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Manila',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chatham',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Chatham',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-10.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-10.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\DeNoronha',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\DeNoronha',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Djibouti',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Djibouti',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pohnpei',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pohnpei',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Porto_Acre',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Porto_Acre',
   'DATA'),
  ('_tcl_data\\msgs\\de.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\de.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Pacific',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Pacific',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Vientiane',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Vientiane',
   'DATA'),
  ('_tcl_data\\encoding\\cp1257.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp1257.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bangui',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bangui',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ulyanovsk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Ulyanovsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Aruba',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Aruba',
   'DATA'),
  ('_tcl_data\\msgs\\is.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\is.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Blanc-Sablon',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Blanc-Sablon',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo200.gif',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\pwrdLogo200.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Chagos',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Chagos',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\NZ',
   'DATA'),
  ('_tcl_data\\msgs\\es_cl.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_cl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+11',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+11',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Sydney',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Sydney',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Faroe',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Faroe',
   'DATA'),
  ('_tcl_data\\msgs\\zh_tw.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\zh_tw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+1',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+1',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\EasterIsland',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Chile\\EasterIsland',
   'DATA'),
  ('_tcl_data\\msgs\\de_be.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\de_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Queensland',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Queensland',
   'DATA'),
  ('_tk_data\\megawidget.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\megawidget.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Blantyre',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Blantyre',
   'DATA'),
  ('_tk_data\\menu.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\menu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Magadan',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Magadan',
   'DATA'),
  ('_tk_data\\ttk\\notebook.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\notebook.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Inuvik',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Inuvik',
   'DATA'),
  ('_tcl_data\\tzdata\\Iceland',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Iceland',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ceuta',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ceuta',
   'DATA'),
  ('_tcl_data\\msgs\\sk.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\sk.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp437.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp437.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tijuana',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Tijuana',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Chuuk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Chuuk',
   'DATA'),
  ('_tcl_data\\msgs\\vi.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\vi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Poland',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Poland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Sakhalin',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Sakhalin',
   'DATA'),
  ('_tcl_data\\msgs\\th.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\th.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Andorra',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Andorra',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Podgorica',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Podgorica',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fakaofo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Fakaofo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Almaty',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Almaty',
   'DATA'),
  ('_tcl_data\\msgs\\ar.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ar.msg',
   'DATA'),
  ('_tcl_data\\msgs\\pt_br.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\pt_br.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp1256.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp1256.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Funafuti',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Funafuti',
   'DATA'),
  ('_tcl_data\\encoding\\macRomania.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macRomania.enc',
   'DATA'),
  ('_tk_data\\images\\tai-ku.gif',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\tai-ku.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Abidjan',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Abidjan',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Calcutta',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Calcutta',
   'DATA'),
  ('_tcl_data\\encoding\\cp737.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp737.enc',
   'DATA'),
  ('_tk_data\\unsupported.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\unsupported.tcl',
   'DATA'),
  ('_tk_data\\msgs\\zh_cn.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\zh_cn.msg',
   'DATA'),
  ('_tk_data\\panedwindow.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\panedwindow.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montreal',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Montreal',
   'DATA'),
  ('_tcl_data\\msgs\\nn.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\nn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Greenwich',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Greenwich',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Mariehamn',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Mariehamn',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jamaica',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Jamaica',
   'DATA'),
  ('_tk_data\\tk.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\tk.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Michigan',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Michigan',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Vienna',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Vienna',
   'DATA'),
  ('_tcl_data\\msgs\\hi.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\hi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Reykjavik',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Reykjavik',
   'DATA'),
  ('_tcl_data\\msgs\\kl.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\kl.msg',
   'DATA'),
  ('_tcl_data\\http1.0\\pkgIndex.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\http1.0\\pkgIndex.tcl',
   'DATA'),
  ('_tk_data\\ttk\\entry.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\entry.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Amman',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Amman',
   'DATA'),
  ('_tcl_data\\tzdata\\Egypt',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Egypt',
   'DATA'),
  ('_tcl_data\\http1.0\\http.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\http1.0\\http.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Rangoon',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Rangoon',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312-raw.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\gb2312-raw.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Niamey',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Niamey',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\West',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\West',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fortaleza',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Fortaleza',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tarawa',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tarawa',
   'DATA'),
  ('_tcl_data\\encoding\\dingbats.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\dingbats.enc',
   'DATA'),
  ('tcl8\\8.5\\msgcat-1.6.1.tm',
   'D:\\Program Files\\Python313\\tcl\\tcl8\\8.5\\msgcat-1.6.1.tm',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\El_Aaiun',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\El_Aaiun',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-7',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-7',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Kinshasa',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Kinshasa',
   'DATA'),
  ('_tcl_data\\encoding\\jis0208.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\jis0208.enc',
   'DATA'),
  ('_tcl_data\\package.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\package.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Baku',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Baku',
   'DATA'),
  ('_tcl_data\\msgs\\en_nz.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_nz.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Hebron',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Hebron',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Salta',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Salta',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Yap',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Yap',
   'DATA'),
  ('_tcl_data\\tm.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tm.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\es_pa.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_pa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Tunis',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Tunis',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5EDT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5EDT',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kolkata',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kolkata',
   'DATA'),
  ('_tk_data\\icons.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\icons.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\en_in.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Montserrat',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Montserrat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Antigua',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Antigua',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Lisbon',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Lisbon',
   'DATA'),
  ('_tcl_data\\msgs\\sw.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\sw.msg',
   'DATA'),
  ('_tk_data\\ttk\\panedwindow.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\panedwindow.tcl',
   'DATA'),
  ('_tk_data\\comdlg.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\comdlg.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\ar_sy.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ar_sy.msg',
   'DATA'),
  ('_tcl_data\\encoding\\shiftjis.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\shiftjis.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Menominee',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Menominee',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chungking',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chungking',
   'DATA'),
  ('_tk_data\\msgbox.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgbox.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Lindeman',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Lindeman',
   'DATA'),
  ('_tcl_data\\msgs\\es_ec.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_ec.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bishkek',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bishkek',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nassau',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nassau',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Bougainville',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Bougainville',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-ru.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\koi8-ru.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7',
   'DATA'),
  ('_tcl_data\\msgs\\id.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\id.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp775.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp775.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Dublin',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Dublin',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qatar',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qatar',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tashkent',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tashkent',
   'DATA'),
  ('_tcl_data\\msgs\\fr_ca.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\fr_ca.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Havana',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Havana',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Prague',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Prague',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bahrain',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bahrain',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayenne',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cayenne',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sao_Paulo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Sao_Paulo',
   'DATA'),
  ('_tcl_data\\opt0.4\\pkgIndex.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\opt0.4\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mbabane',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Mbabane',
   'DATA'),
  ('_tcl_data\\msgs\\ro.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ro.msg',
   'DATA'),
  ('_tcl_data\\msgs\\es_mx.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_mx.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belem',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Belem',
   'DATA'),
  ('_tk_data\\ttk\\aquaTheme.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\aquaTheme.tcl',
   'DATA'),
  ('_tk_data\\ttk\\cursors.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\cursors.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-3.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-3.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Algiers',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Algiers',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grenada',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Grenada',
   'DATA'),
  ('_tk_data\\tclIndex',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\tclIndex',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dominica',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Dominica',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Melbourne',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Melbourne',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Anadyr',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Anadyr',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Norfolk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Norfolk',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Damascus',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Damascus',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\South_Pole',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\South_Pole',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-16.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-16.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\YST9YDT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\YST9YDT',
   'DATA'),
  ('_tcl_data\\encoding\\cp1250.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp1250.enc',
   'DATA'),
  ('_tcl_data\\msgs\\bg.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\bg.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\McMurdo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\McMurdo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Riyadh',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Riyadh',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Riga',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Riga',
   'DATA'),
  ('_tcl_data\\msgs\\ms_my.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ms_my.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-11.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-11.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Moncton',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Moncton',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jerusalem',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jerusalem',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tongatapu',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tongatapu',
   'DATA'),
  ('_tcl_data\\encoding\\jis0201.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\jis0201.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Samoa',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Universal',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\Universal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Shanghai',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Shanghai',
   'DATA'),
  ('_tcl_data\\msgs\\fa_in.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\fa_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Asmara',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Asmara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Gaza',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Gaza',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Brussels',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Brussels',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Palmer',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Palmer',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Belize',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Belize',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Pangnirtung',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Pangnirtung',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+12',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+12',
   'DATA'),
  ('_tcl_data\\msgs\\es_cr.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_cr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ensenada',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Ensenada',
   'DATA'),
  ('_tk_data\\ttk\\defaults.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\defaults.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Mawson',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Mawson',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Cocos',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Cocos',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Apia',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Apia',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-9',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-9',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Bangkok',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Bangkok',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Gibraltar',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Gibraltar',
   'DATA'),
  ('_tcl_data\\msgs\\gv.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\gv.msg',
   'DATA'),
  ('_tk_data\\ttk\\spinbox.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\spinbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\gv_gb.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\gv_gb.msg',
   'DATA'),
  ('_tcl_data\\encoding\\gb2312.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\gb2312.enc',
   'DATA'),
  ('_tcl_data\\encoding\\macCyrillic.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macCyrillic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\AST4ADT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\AST4ADT',
   'DATA'),
  ('_tcl_data\\encoding\\cp855.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp855.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimphu',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Thimphu',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guatemala',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guatemala',
   'DATA'),
  ('_tcl_data\\tzdata\\Kwajalein',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Kwajalein',
   'DATA'),
  ('_tcl_data\\tzdata\\NZ-CHAT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\NZ-CHAT',
   'DATA'),
  ('_tcl_data\\msgs\\et.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\et.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Dawson_Creek',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Dawson_Creek',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\South_Georgia',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\South_Georgia',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Monrovia',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Monrovia',
   'DATA'),
  ('_tcl_data\\msgs\\te.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\te.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\St_Helena',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\St_Helena',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT+0',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Galapagos',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Galapagos',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Singapore',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Singapore',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo100.gif',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\pwrdLogo100.gif',
   'DATA'),
  ('_tcl_data\\msgs\\ga.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ga.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Budapest',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Budapest',
   'DATA'),
  ('_tcl_data\\msgs\\es_gt.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_gt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Ushuaia',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('_tcl_data\\tzdata\\HST',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\HST',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Asuncion',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Asuncion',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Hobart',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Hobart',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuching',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuching',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ulan_Bator',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ulan_Bator',
   'DATA'),
  ('_tcl_data\\tzdata\\MET',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\MET',
   'DATA'),
  ('_tk_data\\ttk\\classicTheme.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\classicTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Nelson',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Fort_Nelson',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\HST10',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\HST10',
   'DATA'),
  ('_tk_data\\iconlist.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\iconlist.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Busingen',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Busingen',
   'DATA'),
  ('_tk_data\\images\\logo100.gif',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\logo100.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\San_Marino',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\San_Marino',
   'DATA'),
  ('_tcl_data\\tzdata\\GB-Eire',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\GB-Eire',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+7',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+7',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Samoa',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Samoa',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macao',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Macao',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Eucla',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Eucla',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ciudad_Juarez',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Ciudad_Juarez',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sarajevo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Sarajevo',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dili',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dili',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\MST7MDT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\MST7MDT',
   'DATA'),
  ('_tcl_data\\tzdata\\PRC',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\PRC',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Qyzylorda',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Qyzylorda',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tbilisi',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tbilisi',
   'DATA'),
  ('_tcl_data\\msgs\\en_be.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Warsaw',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Warsaw',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Knox',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Knox',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Atlantic',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Atlantic',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Punta_Arenas',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Punta_Arenas',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Comoro',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Comoro',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Nipigon',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Nipigon',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Ojinaga',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Ojinaga',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Port_Moresby',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Port_Moresby',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-jp.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso2022-jp.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Irkutsk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Irkutsk',
   'DATA'),
  ('_tcl_data\\encoding\\ksc5601.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\ksc5601.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santo_Domingo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santo_Domingo',
   'DATA'),
  ('_tk_data\\optMenu.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\optMenu.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Navajo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Navajo',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Jersey',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Jersey',
   'DATA'),
  ('_tcl_data\\msgs\\en_zw.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_zw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zurich',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zurich',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-6',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-6',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Beirut',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Beirut',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Simferopol',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Simferopol',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\ACT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\ACT',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+3',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+3',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\CST6',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\CST6',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Isle_of_Man',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Isle_of_Man',
   'DATA'),
  ('tcl8\\8.4\\platform-1.0.19.tm',
   'D:\\Program Files\\Python313\\tcl\\tcl8\\8.4\\platform-1.0.19.tm',
   'DATA'),
  ('_tcl_data\\msgs\\ga_ie.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ga_ie.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Swift_Current',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Swift_Current',
   'DATA'),
  ('_tcl_data\\tzdata\\Jamaica',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Jamaica',
   'DATA'),
  ('_tk_data\\button.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\button.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Los_Angeles',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Los_Angeles',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Stanley',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Stanley',
   'DATA'),
  ('_tk_data\\msgs\\it.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\it.msg',
   'DATA'),
  ('_tk_data\\ttk\\button.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\button.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\cp865.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp865.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pago_Pago',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pago_Pago',
   'DATA'),
  ('_tcl_data\\tzdata\\UTC',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\UTC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\New_York',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\New_York',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tel_Aviv',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tel_Aviv',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Zagreb',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Zagreb',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Cape_Verde',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Cape_Verde',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Saskatchewan',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Saskatchewan',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia_Banderas',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Bahia_Banderas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('_tcl_data\\encoding\\tis-620.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\tis-620.enc',
   'DATA'),
  ('_tcl_data\\msgs\\es_pe.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_pe.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Oslo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Oslo',
   'DATA'),
  ('_tk_data\\pkgIndex.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\pkgIndex.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\DumontDUrville',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\DumontDUrville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuala_Lumpur',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Puerto_Rico',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Puerto_Rico',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Catamarca',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Catamarca',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-t.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\koi8-t.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Mexico\\General',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Mexico\\General',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Banjul',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Banjul',
   'DATA'),
  ('_tcl_data\\msgs\\fa.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\fa.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Khandyga',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Khandyga',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kralendijk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Kralendijk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Virgin',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Virgin',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Port_of_Spain',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Port_of_Spain',
   'DATA'),
  ('_tcl_data\\encoding\\macTurkish.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macTurkish.enc',
   'DATA'),
  ('_tk_data\\msgs\\sv.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\sv.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Luanda',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Luanda',
   'DATA'),
  ('_tcl_data\\encoding\\macThai.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macThai.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-8',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-8',
   'DATA'),
  ('_tcl_data\\msgs\\el.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\EST',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\EST',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Anchorage',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Anchorage',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boise',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Boise',
   'DATA'),
  ('_tk_data\\scrlbar.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\scrlbar.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kirov',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kirov',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-0',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-0',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Recife',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Recife',
   'DATA'),
  ('_tcl_data\\msgs\\es_bo.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_bo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ujung_Pandang',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ujung_Pandang',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rankin_Inlet',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rankin_Inlet',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Curacao',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Curacao',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Jujuy',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Jujuy',
   'DATA'),
  ('_tcl_data\\msgs\\es_pr.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_pr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\North_Dakota\\New_Salem',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Kentucky\\Louisville',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Kentucky\\Louisville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Macau',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Macau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\NSW',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\NSW',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Brunei',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Brunei',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-15.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-15.enc',
   'DATA'),
  ('_tcl_data\\encoding\\gb12345.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\gb12345.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Marquesas',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Marquesas',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Glace_Bay',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Glace_Bay',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\ComodRivadavia',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kiev',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kiev',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kanton',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kanton',
   'DATA'),
  ('_tcl_data\\opt0.4\\optparse.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\opt0.4\\optparse.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Casablanca',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Casablanca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tehran',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tehran',
   'DATA'),
  ('_tcl_data\\msgs\\tr.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\tr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\Zulu',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\Zulu',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Broken_Hill',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Broken_Hill',
   'DATA'),
  ('_tcl_data\\msgs\\lv.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\lv.msg',
   'DATA'),
  ('_tk_data\\ttk\\vistaTheme.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\vistaTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lima',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Lima',
   'DATA'),
  ('_tcl_data\\tzdata\\Portugal',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Portugal',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Novokuznetsk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Novokuznetsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Belfast',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Belfast',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Aqtau',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Aqtau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Malta',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Malta',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Thimbu',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Thimbu',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lusaka',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lusaka',
   'DATA'),
  ('_tcl_data\\msgs\\pt.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\pt.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Bahia',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Bahia',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Syowa',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Syowa',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Conakry',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Conakry',
   'DATA'),
  ('_tcl_data\\msgs\\lt.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\lt.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-6.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-6.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Juneau',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Juneau',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Darwin',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Darwin',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+2',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+2',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Ponape',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Ponape',
   'DATA'),
  ('_tcl_data\\encoding\\cp1252.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp1252.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Perth',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Perth',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Harare',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Harare',
   'DATA'),
  ('_tcl_data\\tzdata\\MST',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\MST',
   'DATA'),
  ('_tcl_data\\encoding\\cp861.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp861.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Malabo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Malabo',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT-0',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\GMT-0',
   'DATA'),
  ('_tcl_data\\msgs\\eo.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tk_data\\mkpsenc.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\mkpsenc.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Mogadishu',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Mogadishu',
   'DATA'),
  ('_tcl_data\\encoding\\iso2022-kr.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso2022-kr.enc',
   'DATA'),
  ('_tcl_data\\msgs\\hr.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\hr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Davis',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Davis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-2',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-2',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Minsk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Minsk',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Troll',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Troll',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Colombo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Colombo',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Timbuktu',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Timbuktu',
   'DATA'),
  ('_tcl_data\\msgs\\ru_ua.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ru_ua.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ms.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ms.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Canberra',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Canberra',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Miquelon',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Miquelon',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Cairo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Cairo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Tucuman',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Tucuman',
   'DATA'),
  ('_tcl_data\\encoding\\symbol.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\symbol.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Noumea',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Noumea',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Thule',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Thule',
   'DATA'),
  ('_tk_data\\ttk\\fonts.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\fonts.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Paramaribo',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Paramaribo',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Halifax',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Halifax',
   'DATA'),
  ('_tcl_data\\msgs\\fo.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\fo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Madeira',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Madeira',
   'DATA'),
  ('_tk_data\\msgs\\fi.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\fi.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Katmandu',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Katmandu',
   'DATA'),
  ('_tcl_data\\encoding\\cp1255.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp1255.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Scoresbysund',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Scoresbysund',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Stockholm',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Stockholm',
   'DATA'),
  ('_tcl_data\\tzdata\\Antarctica\\Rothera',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Antarctica\\Rothera',
   'DATA'),
  ('tcl8\\8.6\\http-2.9.8.tm',
   'D:\\Program Files\\Python313\\tcl\\tcl8\\8.6\\http-2.9.8.tm',
   'DATA'),
  ('_tcl_data\\msgs\\es_do.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_do.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cordoba',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\La_Rioja',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('_tcl_data\\msgs\\kok_in.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\kok_in.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Rome',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Rome',
   'DATA'),
  ('_tcl_data\\msgs\\es_hn.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_hn.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Maldives',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Maldives',
   'DATA'),
  ('_tcl_data\\msgs\\he.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\he.msg',
   'DATA'),
  ('_tcl_data\\encoding\\cp932.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp932.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Tiraspol',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Tiraspol',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chongqing',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chongqing',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Godthab',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Godthab',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guadeloupe',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guadeloupe',
   'DATA'),
  ('_tk_data\\ttk\\scale.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\scale.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Johnston',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Johnston',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ouagadougou',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ouagadougou',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-u.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\koi8-u.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\EST5',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\EST5',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Boa_Vista',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Boa_Vista',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Vancouver',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Vancouver',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\Cordoba',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\Cordoba',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vevay',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vevay',
   'DATA'),
  ('_tcl_data\\msgs\\nl.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\encoding\\jis0212.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\jis0212.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-14',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-14',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Barthelemy',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Barthelemy',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Lucia',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Lucia',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-7.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-7.enc',
   'DATA'),
  ('_tk_data\\msgs\\nl.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\nl.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+9',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+9',
   'DATA'),
  ('_tcl_data\\tzdata\\Chile\\Continental',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Chile\\Continental',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT0',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT0',
   'DATA'),
  ('_tcl_data\\msgs\\gl.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\gl.msg',
   'DATA'),
  ('_tk_data\\ttk\\xpTheme.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\xpTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\St_Kitts',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\St_Kitts',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Yukon',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Yukon',
   'DATA'),
  ('_tk_data\\msgs\\cs.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tk_data\\images\\logoMed.gif',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\logoMed.gif',
   'DATA'),
  ('_tcl_data\\msgs\\es_ve.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_ve.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Libya',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Libya',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Fiji',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Fiji',
   'DATA'),
  ('_tcl_data\\encoding\\cp852.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp852.enc',
   'DATA'),
  ('_tk_data\\xmfbox.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\xmfbox.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\cs.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\cs.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Efate',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Efate',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Pitcairn',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Pitcairn',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo150.gif',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\pwrdLogo150.gif',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo75.gif',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\pwrdLogo75.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Caracas',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Caracas',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Kaliningrad',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Kaliningrad',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Panama',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Panama',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kamchatka',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kamchatka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Yellowknife',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Yellowknife',
   'DATA'),
  ('_tk_data\\tkfbox.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\tkfbox.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\euc-cn.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\euc-cn.enc',
   'DATA'),
  ('_tcl_data\\encoding\\cp949.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp949.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+0',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+0',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+6',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+6',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Mountain',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Mountain',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Pacific',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Pacific',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-4.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-4.enc',
   'DATA'),
  ('_tk_data\\msgs\\el.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\el.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Nouakchott',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Nouakchott',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Jan_Mayen',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Gaborone',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Gaborone',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Tomsk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Tomsk',
   'DATA'),
  ('_tcl_data\\encoding\\koi8-r.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\koi8-r.enc',
   'DATA'),
  ('_tk_data\\msgs\\pl.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\pl.msg',
   'DATA'),
  ('_tk_data\\msgs\\eo.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\eo.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Mexico_City',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Mexico_City',
   'DATA'),
  ('_tcl_data\\safe.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\safe.tcl',
   'DATA'),
  ('_tk_data\\ttk\\altTheme.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\altTheme.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tortola',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Tortola',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Tahiti',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Tahiti',
   'DATA'),
  ('_tk_data\\images\\logoLarge.gif',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\logoLarge.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Regina',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Regina',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santiago',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santiago',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Chita',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Chita',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Juan',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Juan',
   'DATA'),
  ('_tcl_data\\msgs\\kw.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\kw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\PST8PDT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Tegucigalpa',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Tegucigalpa',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\La_Paz',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\La_Paz',
   'DATA'),
  ('_tcl_data\\msgs\\ta.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ta.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\ROC',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\ROC',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Vincennes',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Vincennes',
   'DATA'),
  ('_tcl_data\\tzdata\\SystemV\\PST8PDT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\SystemV\\PST8PDT',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Eirunepe',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Eirunepe',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Fort_Wayne',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Fort_Wayne',
   'DATA'),
  ('_tcl_data\\encoding\\cp950.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp950.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Resolute',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Resolute',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Muscat',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Muscat',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Bissau',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Bissau',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Bucharest',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Bucharest',
   'DATA'),
  ('_tk_data\\text.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\text.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\GMT0',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\GMT0',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Mauritius',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Mauritius',
   'DATA'),
  ('_tcl_data\\msgs\\en_bw.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_bw.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Azores',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Azores',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Seoul',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Seoul',
   'DATA'),
  ('_tk_data\\ttk\\ttk.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\ttk.tcl',
   'DATA'),
  ('_tcl_data\\word.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\word.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Palau',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Palau',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Newfoundland',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Newfoundland',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dacca',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dacca',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Dhaka',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Dhaka',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Santa_Isabel',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Santa_Isabel',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Dakar',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Dakar',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Guyana',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Guyana',
   'DATA'),
  ('_tcl_data\\init.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\init.tcl',
   'DATA'),
  ('_tcl_data\\msgs\\hu.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\hu.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Moscow',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Moscow',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Denver',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Denver',
   'DATA'),
  ('_tcl_data\\tzdata\\Arctic\\Longyearbyen',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Arctic\\Longyearbyen',
   'DATA'),
  ('_tcl_data\\encoding\\cp869.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp869.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Oral',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Oral',
   'DATA'),
  ('_tcl_data\\encoding\\cp1253.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp1253.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Indian\\Reunion',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Indian\\Reunion',
   'DATA'),
  ('_tcl_data\\msgs\\ko.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ko.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\Adelaide',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\Adelaide',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Indiana\\Winamac',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Indiana\\Winamac',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT+10',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT+10',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Uzhgorod',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Uzhgorod',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Monaco',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Monaco',
   'DATA'),
  ('_tcl_data\\msgs\\en_hk.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\en_hk.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Kiritimati',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Kiritimati',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Karachi',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Karachi',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Atikokan',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Atikokan',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Lagos',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Lagos',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Argentina\\San_Luis',
   'D:\\Program '
   'Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Argentina\\San_Luis',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-3',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-3',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-13',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-13',
   'DATA'),
  ('_tcl_data\\msgs\\it.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\it.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Urumqi',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Urumqi',
   'DATA'),
  ('_tcl_data\\tzdata\\Atlantic\\Bermuda',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Atlantic\\Bermuda',
   'DATA'),
  ('_tcl_data\\encoding\\cp936.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\cp936.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Etc\\GMT-4',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Etc\\GMT-4',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Eastern',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Eastern',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Brazzaville',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Brazzaville',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Ashgabat',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Ashgabat',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Sitka',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Sitka',
   'DATA'),
  ('_tcl_data\\msgs\\zh.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\zh.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\UCT',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\UCT',
   'DATA'),
  ('_tcl_data\\msgs\\kok.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\kok.msg',
   'DATA'),
  ('_tcl_data\\auto.tcl',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\auto.tcl',
   'DATA'),
  ('_tk_data\\ttk\\scrollbar.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\ttk\\scrollbar.tcl',
   'DATA'),
  ('_tcl_data\\encoding\\macRoman.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macRoman.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Costa_Rica',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Costa_Rica',
   'DATA'),
  ('_tk_data\\images\\logo64.gif',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\logo64.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Wake',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Wake',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Harbin',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Harbin',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Ljubljana',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Ljubljana',
   'DATA'),
  ('_tcl_data\\encoding\\macCroatian.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\macCroatian.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Alaska',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Alaska',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cayman',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cayman',
   'DATA'),
  ('_tcl_data\\tzdata\\Brazil\\Acre',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Brazil\\Acre',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Sofia',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Sofia',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Campo_Grande',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Campo_Grande',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Matamoros',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Matamoros',
   'DATA'),
  ('_tk_data\\bgerror.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\bgerror.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Australia\\West',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Australia\\West',
   'DATA'),
  ('_tcl_data\\msgs\\fa_ir.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\fa_ir.msg',
   'DATA'),
  ('_tcl_data\\msgs\\ja.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\ja.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-13.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-13.enc',
   'DATA'),
  ('_tcl_data\\msgs\\nl_be.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\nl_be.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Jakarta',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Jakarta',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Accra',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Accra',
   'DATA'),
  ('_tk_data\\msgs\\fr.msg',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\msgs\\fr.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Grand_Turk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Grand_Turk',
   'DATA'),
  ('_tcl_data\\msgs\\es_uy.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\es_uy.msg',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Lower_Princes',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Lower_Princes',
   'DATA'),
  ('_tcl_data\\tzdata\\Pacific\\Niue',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Pacific\\Niue',
   'DATA'),
  ('_tk_data\\images\\pwrdLogo175.gif',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\images\\pwrdLogo175.gif',
   'DATA'),
  ('_tcl_data\\tzdata\\Africa\\Ndjamena',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Africa\\Ndjamena',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Cancun',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Cancun',
   'DATA'),
  ('_tcl_data\\msgs\\zh_hk.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\zh_hk.msg',
   'DATA'),
  ('_tk_data\\fontchooser.tcl',
   'D:\\Program Files\\Python313\\tcl\\tk8.6\\fontchooser.tcl',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Samara',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Samara',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Yakutsk',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Yakutsk',
   'DATA'),
  ('_tcl_data\\tzdata\\America\\Rainy_River',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\America\\Rainy_River',
   'DATA'),
  ('_tcl_data\\tzdata\\US\\Arizona',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\US\\Arizona',
   'DATA'),
  ('_tcl_data\\msgs\\de_at.msg',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\msgs\\de_at.msg',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-8.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-8.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Canada\\Central',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Canada\\Central',
   'DATA'),
  ('_tcl_data\\encoding\\iso8859-14.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\iso8859-14.enc',
   'DATA'),
  ('_tcl_data\\encoding\\ebcdic.enc',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\encoding\\ebcdic.enc',
   'DATA'),
  ('_tcl_data\\tzdata\\Europe\\Istanbul',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Europe\\Istanbul',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Samarkand',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Samarkand',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kuwait',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kuwait',
   'DATA'),
  ('_tcl_data\\tzdata\\Asia\\Kashgar',
   'D:\\Program Files\\Python313\\tcl\\tcl8.6\\tzdata\\Asia\\Kashgar',
   'DATA'),
  ('numpy-2.2.6.dist-info\\DELVEWHEEL',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\DELVEWHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('numpy-2.2.6.dist-info\\WHEEL',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('numpy-2.2.6.dist-info\\METADATA',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\RECORD',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('numpy-2.2.6.dist-info\\entry_points.txt',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\entry_points.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\LICENSE.txt',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\LICENSE.txt',
   'DATA'),
  ('numpy-2.2.6.dist-info\\INSTALLER',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\numpy-2.2.6.dist-info\\INSTALLER',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'D:\\Program '
   'Files\\Python313\\Lib\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Documents\\augment-projects\\com '
   'assistant\\build\\SerialDataVisualizer_Professional\\base_library.zip',
   'DATA')],
 [('functools', 'D:\\Program Files\\Python313\\Lib\\functools.py', 'PYMODULE'),
  ('enum', 'D:\\Program Files\\Python313\\Lib\\enum.py', 'PYMODULE'),
  ('_collections_abc',
   'D:\\Program Files\\Python313\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('weakref', 'D:\\Program Files\\Python313\\Lib\\weakref.py', 'PYMODULE'),
  ('locale', 'D:\\Program Files\\Python313\\Lib\\locale.py', 'PYMODULE'),
  ('sre_constants',
   'D:\\Program Files\\Python313\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('io', 'D:\\Program Files\\Python313\\Lib\\io.py', 'PYMODULE'),
  ('genericpath',
   'D:\\Program Files\\Python313\\Lib\\genericpath.py',
   'PYMODULE'),
  ('traceback', 'D:\\Program Files\\Python313\\Lib\\traceback.py', 'PYMODULE'),
  ('copyreg', 'D:\\Program Files\\Python313\\Lib\\copyreg.py', 'PYMODULE'),
  ('posixpath', 'D:\\Program Files\\Python313\\Lib\\posixpath.py', 'PYMODULE'),
  ('ntpath', 'D:\\Program Files\\Python313\\Lib\\ntpath.py', 'PYMODULE'),
  ('sre_parse', 'D:\\Program Files\\Python313\\Lib\\sre_parse.py', 'PYMODULE'),
  ('warnings', 'D:\\Program Files\\Python313\\Lib\\warnings.py', 'PYMODULE'),
  ('_weakrefset',
   'D:\\Program Files\\Python313\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('abc', 'D:\\Program Files\\Python313\\Lib\\abc.py', 'PYMODULE'),
  ('operator', 'D:\\Program Files\\Python313\\Lib\\operator.py', 'PYMODULE'),
  ('collections',
   'D:\\Program Files\\Python313\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('types', 'D:\\Program Files\\Python313\\Lib\\types.py', 'PYMODULE'),
  ('linecache', 'D:\\Program Files\\Python313\\Lib\\linecache.py', 'PYMODULE'),
  ('reprlib', 'D:\\Program Files\\Python313\\Lib\\reprlib.py', 'PYMODULE'),
  ('stat', 'D:\\Program Files\\Python313\\Lib\\stat.py', 'PYMODULE'),
  ('heapq', 'D:\\Program Files\\Python313\\Lib\\heapq.py', 'PYMODULE'),
  ('sre_compile',
   'D:\\Program Files\\Python313\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('codecs', 'D:\\Program Files\\Python313\\Lib\\codecs.py', 'PYMODULE'),
  ('keyword', 'D:\\Program Files\\Python313\\Lib\\keyword.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Program Files\\Python313\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\Program Files\\Python313\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Program Files\\Python313\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\Program Files\\Python313\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\Program Files\\Python313\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Program Files\\Python313\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Program Files\\Python313\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\Program Files\\Python313\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Program Files\\Python313\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Program Files\\Python313\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\Program Files\\Python313\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Program Files\\Python313\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Program Files\\Python313\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\Program Files\\Python313\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Program Files\\Python313\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Program Files\\Python313\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Program Files\\Python313\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\Program Files\\Python313\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Program Files\\Python313\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Program Files\\Python313\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\Program Files\\Python313\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\Program Files\\Python313\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\Program Files\\Python313\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\Program Files\\Python313\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\Program Files\\Python313\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Program Files\\Python313\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Program Files\\Python313\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Program Files\\Python313\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Program Files\\Python313\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Program Files\\Python313\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Program Files\\Python313\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Program Files\\Python313\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Program Files\\Python313\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Program Files\\Python313\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Program Files\\Python313\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\Program Files\\Python313\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\Program Files\\Python313\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\Program Files\\Python313\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\Program Files\\Python313\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\Program Files\\Python313\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\Program Files\\Python313\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Program Files\\Python313\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\Program Files\\Python313\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'D:\\Program Files\\Python313\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Program Files\\Python313\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Program Files\\Python313\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\Program Files\\Python313\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\Program Files\\Python313\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\Program Files\\Python313\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\Program Files\\Python313\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\Program Files\\Python313\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Program Files\\Python313\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Program Files\\Python313\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\Program Files\\Python313\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\Program Files\\Python313\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Program Files\\Python313\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Program Files\\Python313\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\Program Files\\Python313\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Program Files\\Python313\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\Program Files\\Python313\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\Program Files\\Python313\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\Program Files\\Python313\\Lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('re._parser',
   'D:\\Program Files\\Python313\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'D:\\Program Files\\Python313\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'D:\\Program Files\\Python313\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'D:\\Program Files\\Python313\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re', 'D:\\Program Files\\Python313\\Lib\\re\\__init__.py', 'PYMODULE'),
  ('os', 'D:\\Program Files\\Python313\\Lib\\os.py', 'PYMODULE')])
