#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速打包脚本
版权归属: 米醋电子工作室
"""

import os
import sys
import subprocess
from datetime import datetime


def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['SerialDataVisualizer.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'serial',
        'serial.tools.list_ports',
        'numpy',
        'configparser',
        'csv',
        'logging',
        'logging.handlers',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'PyQt5',
        'PyQt6',
        'tkinter',
        'matplotlib',
        'scipy',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SerialDataVisualizer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
'''
    
    with open('SerialDataVisualizer.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ PyInstaller规格文件已创建")


def build_executable():
    """构建可执行文件"""
    print("开始构建可执行文件...")
    
    try:
        # 检查PyInstaller
        result = subprocess.run([sys.executable, '-c', 'import PyInstaller'], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print("PyInstaller未安装，正在安装...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        
        # 创建规格文件
        create_spec_file()
        
        # 运行PyInstaller
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'SerialDataVisualizer.spec']
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 可执行文件构建成功!")
            
            # 检查输出文件
            exe_path = os.path.join('dist', 'SerialDataVisualizer.exe')
            if os.path.exists(exe_path):
                size_mb = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"✓ 可执行文件: {exe_path} ({size_mb:.1f} MB)")
                return exe_path
            else:
                print("✗ 可执行文件未找到")
                return None
        else:
            print("✗ 构建失败:")
            print(result.stderr)
            return None
            
    except Exception as e:
        print(f"✗ 构建过程出错: {e}")
        return None


def create_release_package(exe_path):
    """创建发布包"""
    if not exe_path or not os.path.exists(exe_path):
        return None
    
    print("\n创建发布包...")
    
    # 创建发布目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    release_dir = f"SerialDataVisualizer_Release_{timestamp}"
    
    if os.path.exists(release_dir):
        import shutil
        shutil.rmtree(release_dir)
    os.makedirs(release_dir, exist_ok=True)
    
    try:
        import shutil
        
        # 复制可执行文件
        shutil.copy2(exe_path, release_dir)
        print(f"✓ 复制可执行文件到: {release_dir}")
        
        # 创建使用说明
        readme_content = f"""# 串口数据可视化软件 v1.0

## 使用方法

### 1. 快速开始
双击 `SerialDataVisualizer.exe` 启动程序

### 2. 命令行使用
```
SerialDataVisualizer.exe --help          # 查看帮助
SerialDataVisualizer.exe --demo          # 运行演示模式
SerialDataVisualizer.exe --port COM3     # 指定串口
```

### 3. 数据格式
单片机发送数据格式 (10个数值):
```
1000,1500,2000,2500,3000,100,200,300,400,500
```
- 前5个: 传感器数据 (用于可视化)
- 后5个: 扩展数据

### 4. 传感器位置
- 传感器1: 中心 (0.5, 0.5)
- 传感器2: 右上 (1.0, 1.0)
- 传感器3: 右下 (1.0, 0.0)
- 传感器4: 左上 (0.0, 1.0)
- 传感器5: 左下 (0.0, 0.0)

### 5. 操作说明
- 程序启动后会自动扫描可用串口
- 选择正确的端口和波特率
- 按 'r' 开始/停止记录数据
- 按 'e' 导出数据到CSV文件
- 按 'q' 退出程序

### 6. 输出文件
- 数据导出: exports/ 目录下的CSV文件
- 日志文件: serial_visualizer.log
- 配置文件: config.ini

### 7. 系统要求
- Windows 10/11 64位
- 无需安装Python环境
- 支持便携式运行

---
版权归属: 米醋电子工作室
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        with open(os.path.join(release_dir, 'README.txt'), 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✓ 使用说明已创建")
        
        # 创建启动脚本
        batch_content = f"""@echo off
chcp 65001 >nul
title 串口数据可视化软件
echo.
echo ========================================
echo    串口数据可视化软件 v1.0
echo    版权归属: 米醋电子工作室
echo ========================================
echo.
echo 正在启动程序...
echo.
SerialDataVisualizer.exe
echo.
echo 程序已退出，按任意键关闭窗口...
pause >nul
"""
        
        with open(os.path.join(release_dir, '启动程序.bat'), 'w', encoding='gbk') as f:
            f.write(batch_content)
        
        print("✓ 启动脚本已创建")
        
        # 创建演示脚本
        demo_batch = f"""@echo off
chcp 65001 >nul
title 串口数据可视化软件 - 演示模式
echo.
echo ========================================
echo    串口数据可视化软件 - 演示模式
echo    版权归属: 米醋电子工作室
echo ========================================
echo.
echo 正在启动演示模式...
echo 注意: 使用模拟数据进行演示
echo.
SerialDataVisualizer.exe --demo
echo.
echo 演示结束，按任意键关闭窗口...
pause >nul
"""
        
        with open(os.path.join(release_dir, '演示模式.bat'), 'w', encoding='gbk') as f:
            f.write(demo_batch)
        
        print("✓ 演示脚本已创建")
        
        return release_dir
        
    except Exception as e:
        print(f"✗ 创建发布包失败: {e}")
        return None


def main():
    """主函数"""
    print("=" * 60)
    print("           串口数据可视化软件 - 打包工具")
    print("              版权归属: 米醋电子工作室")
    print("=" * 60)
    print()
    
    # 构建可执行文件
    exe_path = build_executable()
    if not exe_path:
        print("\n❌ 构建失败!")
        return 1
    
    # 创建发布包
    release_dir = create_release_package(exe_path)
    if release_dir:
        print(f"\n🎉 打包完成!")
        print(f"发布包位置: {release_dir}")
        print(f"可执行文件: {exe_path}")
        print("\n📋 使用说明:")
        print("1. 进入发布包目录")
        print("2. 双击 '启动程序.bat' 启动软件")
        print("3. 或双击 '演示模式.bat' 查看演示")
        print("4. 查看 README.txt 了解详细使用方法")
    else:
        print("\n⚠️ 发布包创建失败，但可执行文件已生成")
        print(f"可执行文件位置: {exe_path}")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
