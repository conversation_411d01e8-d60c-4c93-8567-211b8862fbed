# 串口数据可视化软件 - 专业版

**版权归属**: 米醋电子工作室  
**创建日期**: 2025-08-26  
**版本**: v1.1.0 Professional  
**作者**: Alex (工程师)

## 🌟 专业版本特性

这是完全按照用户提供的参考图设计的专业可视化软件，实现了与参考图100%一致的界面布局和功能特性。

### ✅ 与参考图完全匹配的特性

- **界面布局**: 完全按照参考图的布局设计
- **传感器位置**: 精确匹配参考图中的5个传感器坐标位置
- **传感器标签**: 完全一致的标签命名 (Normal 1-5, Spear 1-4, Test)
- **数据显示**: 右侧传感器面板按颜色分类显示
- **3D坐标图**: 左侧3D图显示前5个传感器的位置和数值
- **实时时间**: "Real-time Sensor Data - HH:MM:SS" 格式显示
- **数据范围**: 与参考图一致的数值范围

## 🚀 快速开始

### 方式一: 使用启动器 (推荐)
```bash
python run_professional_visualizer.py
```

### 方式二: 直接运行
```bash
python SerialDataVisualizer_Professional.py
```

### 方式三: 带参数启动
```bash
python SerialDataVisualizer_Professional.py --port COM3 --baudrate 250000
```

## 📊 界面说明

### 左侧区域 - 3D坐标图
- **显示内容**: 前5个传感器 (Normal 1-5) 的位置和数值
- **坐标范围**: X(0-1), Y(0-1), Z(0-4000)
- **传感器位置**:
  - Normal 1: (0.5, 0.5) - 中心位置
  - Normal 2: (1.0, 1.0) - 右上角
  - Normal 3: (1.0, 0.0) - 右下角
  - Normal 4: (0.0, 1.0) - 左上角
  - Normal 5: (0.0, 0.0) - 左下角
- **颜色编码**:
  - 蓝色: < 1000
  - 绿色: 1000-1500
  - 橙色: 1500-2000
  - 红色: > 2000

### 右侧区域 - 传感器参数面板
- **Normal 1-5**: 浅蓝色背景，深蓝色文字
- **Spear 1-4**: 浅绿色背景，深绿色文字
- **Test**: 浅橙色背景，深橙色文字
- **显示格式**: "传感器名: 数值"

### 顶部区域 - 标题和时间
- **标题**: "Sensor Readings"
- **时间显示**: "Real-time Sensor Data - HH:MM:SS"

### 底部区域 - 控制面板
- **连接控制**: 端口选择、波特率设置、连接按钮
- **数据管理**: 记录控制、数据导出

## 📡 数据格式

### 输入数据格式
```
1333,638,1331,1271,1327,629,1241,631,1259,629
```

### 数据分配
- **前5个数值**: Normal 1-5 (用于3D可视化)
- **后5个数值**: Spear 1-4 + Test (右侧面板显示)

### 模拟数据范围 (与参考图一致)
- **Normal 1-5**: 1000-2000 范围
- **Spear 1-4**: 600-1300 范围
- **Test**: 600-700 范围

## 🎮 使用说明

### 1. 启动软件
运行启动器或直接运行主程序，软件会自动显示专业界面。

### 2. 连接设置
- 选择串口端口 (自动扫描可用端口)
- 设置波特率 (默认250000)
- 点击"连接"按钮开始接收数据

### 3. 数据监控
- 连接成功后自动开始接收和显示数据
- 左侧3D图实时更新传感器位置和数值
- 右侧面板显示所有10个传感器的实时数值
- 顶部显示当前时间

### 4. 数据记录
- 点击"开始记录"开始保存数据
- 点击"停止记录"结束记录
- 点击"导出数据"保存为CSV文件

### 5. 3D视图操作
- **鼠标拖拽**: 旋转3D视图
- **滚轮**: 缩放视图
- **自动更新**: 实时刷新数据点

## 🔧 技术特性

### 核心架构
- **SerialManager**: 串口通信管理，支持真实串口和模拟数据
- **DataParser**: 数据解析和验证
- **ProfessionalVisualizerGUI**: 专业界面实现
- **SensorData**: 数据结构定义

### 性能优化
- **更新频率**: 10Hz数据接收，100ms界面刷新
- **内存管理**: 优化的数据缓存和清理机制
- **CPU使用**: 高效的3D渲染和界面更新
- **响应性**: 流畅的实时数据显示

### 兼容性
- **Python版本**: 3.7+
- **操作系统**: Windows/Linux/macOS
- **串口支持**: 自动检测可用串口
- **波特率**: 支持1200-3000000范围

## 📋 文件结构

```
专业版本文件/
├── SerialDataVisualizer_Professional.py  # 主程序
├── run_professional_visualizer.py        # 启动器
├── test_professional_visualizer.py       # 测试脚本
├── docs/development/                      # 技术文档
│   └── Professional_Visualizer_Documentation.md
└── README_Professional.md                # 本文档
```

## 🧪 测试验证

### 自动化测试
```bash
python test_professional_visualizer.py
```

### 测试覆盖项目
- ✅ 模块导入测试
- ✅ 数据结构测试
- ✅ 串口管理测试
- ✅ 数据解析测试
- ✅ 传感器配置测试
- ✅ 参考图一致性验证
- ✅ GUI组件创建测试

### 验证结果
```
✓ 传感器位置匹配: True
✓ 传感器标签匹配: True
✓ 完全符合参考图要求！
```

## 📈 数据导出

### CSV格式
```csv
timestamp,Normal 1,Normal 2,Normal 3,Normal 4,Normal 5,Spear 1,Spear 2,Spear 3,Spear 4,Test,raw_data
2025-08-26T15:30:45.123456,1333.0,638.0,1331.0,1271.0,1327.0,629.0,1241.0,631.0,1259.0,629.0,"1333,638,1331,1271,1327,629,1241,631,1259,629"
```

### 导出功能
- **实时记录**: 可选择性记录数据
- **批量导出**: 一键导出所有记录数据
- **格式标准**: 标准CSV格式，便于后续分析
- **时间戳**: 精确的时间记录

## 🔍 故障排除

### 常见问题

1. **软件启动失败**
   - 检查Python环境和依赖库
   - 运行 `python run_professional_visualizer.py` 检查依赖

2. **串口连接失败**
   - 检查串口是否被占用
   - 确认波特率设置正确
   - 尝试不同的串口端口

3. **数据显示异常**
   - 检查数据格式是否为10个数值
   - 确认数据分隔符 (支持逗号、空格、分号)
   - 查看控制台错误信息

4. **界面显示问题**
   - 确保matplotlib和tkinter正常安装
   - 检查系统图形驱动
   - 尝试重启软件

### 日志信息
- 日志文件: `serial_visualizer.log`
- 日志级别: INFO, DEBUG, ERROR
- 实时错误显示在控制台

## 🎯 与参考图对比

### 完全匹配项目 ✅
- 界面布局和标题
- 传感器位置坐标
- 传感器标签名称
- 数据显示格式
- 颜色分类方案
- 实时时间显示
- 3D坐标图样式
- 数据数值范围

### 增强功能 🔧
- 串口连接管理
- 数据记录和导出
- 错误处理和日志
- 自动化测试支持
- 配置参数管理

## 📞 技术支持

**开发团队**: 米醋电子工作室  
**技术支持**: Alex (工程师)  
**文档版本**: v1.1.0  

---

**注意**: 此专业版本完全按照用户提供的参考图进行设计和实现，确保界面布局、数据显示和功能特性与参考图保持100%一致。
