# 产品需求文档 (PRD) - 串口数据可视化软件

## 1. 文档信息
- **项目名称**: 串口数据可视化软件 (Serial Data Visualizer)
- **版本**: v1.0
- **创建日期**: 2025-08-26
- **负责人**: Emma (产品经理)
- **最后更新**: 2025-08-26
- **版权归属**: 米醋电子工作室

## 2. 背景与问题陈述

### 2.1 项目背景
用户需要开发一个专用软件，用于接收单片机通过串口发送的传感器数据，并进行实时3D可视化展示。该软件主要用于监控和分析传感器阵列的数据变化。

### 2.2 核心问题
- **数据接收问题**: 需要稳定可靠地从串口接收单片机发送的数据
- **数据解析问题**: 需要正确解析10个数据为一组的数据包格式
- **可视化展示问题**: 需要将传感器数据以3D形式直观展示
- **实时性问题**: 需要实现数据的实时更新和显示

### 2.3 解决的痛点
- 传统的串口调试工具只能显示原始数据，无法直观展示传感器状态
- 缺乏专门的传感器数据可视化工具
- 需要实时监控多个传感器的状态变化

## 3. 目标与成功指标

### 3.1 项目目标 (Objectives)
- **O1**: 实现稳定的串口数据接收功能，支持常见波特率
- **O2**: 开发直观的3D可视化界面，清晰展示传感器布局和数据
- **O3**: 提供实时数据更新和历史数据查看功能
- **O4**: 确保软件运行稳定，支持长时间连续监控

### 3.2 关键结果 (Key Results)
- **KR1**: 串口连接成功率 ≥ 99%，数据丢包率 < 0.1%
- **KR2**: 3D可视化刷新率 ≥ 30fps，数据更新延迟 < 100ms
- **KR3**: 支持至少5种常见波特率 (9600, 19200, 38400, 57600, 115200)
- **KR4**: 软件连续运行24小时无崩溃

### 3.3 反向指标 (Counter Metrics)
- CPU使用率不超过20%
- 内存占用不超过100MB
- 界面响应时间不超过200ms

## 4. 用户画像与用户故事

### 4.1 目标用户
- **主要用户**: 嵌入式开发工程师、传感器测试人员
- **次要用户**: 学生、研究人员、电子爱好者

### 4.2 用户故事
- **US1**: 作为开发工程师，我希望能够实时查看传感器数据，以便监控系统运行状态
- **US2**: 作为测试人员，我希望能够直观地看到传感器在不同位置的数值变化
- **US3**: 作为研究人员，我希望能够保存和回放历史数据，以便进行数据分析
- **US4**: 作为用户，我希望软件界面简洁易用，能够快速上手

## 5. 功能规格详述

### 5.1 核心功能模块

#### 5.1.1 串口通信模块
- **功能描述**: 负责与单片机建立串口连接，接收数据
- **技术要求**:
  - 支持COM1-COM20端口自动检测
  - 支持波特率: 9600, 19200, 38400, 57600, 115200
  - 支持数据位: 8位，停止位: 1位，校验位: 无
  - 实现数据缓冲和错误处理机制

#### 5.1.2 数据解析模块
- **功能描述**: 解析接收到的数据包，提取传感器数值
- **数据格式规范**:
  - 每组数据包含10个数值
  - 前5个数值对应传感器读数，位置坐标分别为:
    - 传感器1: (0.5, 0.5) - 中心位置
    - 传感器2: (1, 1) - 右上角
    - 传感器3: (1, 0) - 右下角  
    - 传感器4: (0, 1) - 左上角
    - 传感器5: (0, 0) - 左下角
  - 后5个数值为扩展数据或状态信息

#### 5.1.3 3D可视化模块
- **功能描述**: 将传感器数据以3D形式展示
- **可视化要求**:
  - 3D坐标系: X轴(0-1), Y轴(0-1), Z轴(数据值)
  - 传感器点用不同颜色的球体表示
  - 数据值通过Z轴高度和颜色渐变表示
  - 支持鼠标旋转、缩放、平移操作
  - 显示坐标轴标签和数值

#### 5.1.4 实时数据显示模块
- **功能描述**: 在界面右侧显示实时数据数值
- **显示要求**:
  - 显示当前时间戳
  - 分别显示5个传感器的实时数值
  - 数值变化时高亮显示
  - 支持数值范围设置和异常报警

### 5.2 辅助功能模块

#### 5.2.1 连接管理
- 串口参数配置界面
- 连接状态指示
- 自动重连机制

#### 5.2.2 数据记录
- 数据日志记录功能
- 支持CSV格式导出
- 历史数据回放功能

#### 5.2.3 界面控制
- 暂停/继续数据更新
- 清空显示数据
- 视图重置功能

## 6. 范围定义

### 6.1 包含功能 (In Scope)
- 串口数据接收和解析
- 3D实时数据可视化
- 基本的数据记录和导出
- 简洁的用户界面
- 连接状态管理

### 6.2 排除功能 (Out of Scope)
- 复杂的数据分析算法
- 网络数据传输功能
- 多设备同时连接
- 高级数据处理和滤波
- 移动端支持

## 7. 依赖与风险

### 7.1 技术依赖
- Python 3.8+ 运行环境
- PyQt5/PyQt6 GUI框架
- PySerial 串口通信库
- PyOpenGL 3D图形渲染
- NumPy 数据处理

### 7.2 潜在风险
- **高风险**: 串口通信稳定性问题
- **中风险**: 3D渲染性能问题
- **低风险**: 不同操作系统兼容性问题

### 7.3 风险缓解策略
- 实现完善的错误处理和重连机制
- 优化3D渲染算法，使用硬件加速
- 在多个操作系统平台进行测试

## 8. 发布初步计划

### 8.1 开发阶段
- **阶段1**: 串口通信模块开发 (2天)
- **阶段2**: 数据解析和3D可视化 (3天)
- **阶段3**: 界面集成和优化 (2天)
- **阶段4**: 测试和调试 (1天)

### 8.2 测试计划
- 单元测试: 各模块功能测试
- 集成测试: 端到端数据流测试
- 性能测试: 长时间运行稳定性测试
- 兼容性测试: 不同操作系统测试

### 8.3 发布策略
- 内部测试版本
- Beta版本用户测试
- 正式版本发布

---

**文档生成完成时间**: 2025-08-26
**下一步行动**: 等待Mike指令，准备进入技术架构设计阶段
