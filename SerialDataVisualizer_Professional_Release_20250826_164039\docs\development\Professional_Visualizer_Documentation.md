# 专业可视化软件技术文档

**版权归属**: 米醋电子工作室  
**创建日期**: 2025-08-26  
**作者**: <PERSON> (工程师)  
**版本**: v1.1.0

## 概述

专业可视化软件 (`SerialDataVisualizer_Professional.py`) 是按照用户提供的参考图设计的高级串口数据可视化工具。该软件完全符合参考图的界面布局和数据显示要求。

## 核心特性

### 1. 界面布局 (完全符合参考图)

- **标题区域**: "Sensor Readings" 标题
- **左侧区域**: 3D坐标图显示实时传感器数据
- **右侧区域**: 传感器参数列表，按颜色分类显示
- **底部区域**: 连接控制和数据管理面板
- **时间显示**: 实时时间显示 "Real-time Sensor Data - HH:MM:SS"

### 2. 传感器数据配置

#### 传感器位置 (与参考图完全一致)
```python
SENSOR_POSITIONS = [
    (0.5, 0.5),  # Normal 1 - 中心位置
    (1.0, 1.0),  # Normal 2 - 右上角
    (1.0, 0.0),  # Normal 3 - 右下角
    (0.0, 1.0),  # Normal 4 - 左上角
    (0.0, 0.0),  # Normal 5 - 左下角
]
```

#### 传感器标签 (与参考图完全一致)
```python
SENSOR_LABELS = [
    "Normal 1", "Normal 2", "Normal 3", "Normal 4", "Normal 5",
    "Spear 1", "Spear 2", "Spear 3", "Spear 4", "Test"
]
```

### 3. 数据显示特性

#### 右侧传感器面板
- **Normal 1-5**: 浅蓝色背景，深蓝色文字
- **Spear 1-4**: 浅绿色背景，深绿色文字  
- **Test**: 浅橙色背景，深橙色文字
- **实时数值更新**: 显示格式 "传感器名: 数值"

#### 左侧3D坐标图
- **坐标范围**: X(0-1), Y(0-1), Z(0-4000)
- **数据点**: 前5个传感器 (Normal 1-5) 的位置和数值
- **颜色编码**: 
  - 蓝色: < 1000
  - 绿色: 1000-1500
  - 橙色: 1500-2000
  - 红色: > 2000
- **数值标签**: 每个数据点显示具体数值

### 4. 数据生成规则

#### 模拟数据范围 (与参考图一致)
- **Normal 1-5**: 1000-2000 范围内随机值
- **Spear 1-4**: 600-1300 范围内随机值
- **Test**: 600-700 范围内随机值

#### 数据更新频率
- **更新频率**: 10Hz (每100ms更新一次)
- **显示刷新**: 实时更新所有界面元素

## 技术架构

### 1. 核心类结构

```python
# 数据结构
class SensorData:
    - timestamp: 时间戳
    - sensor_values: Normal 1-5 数据
    - extended_data: Spear 1-4 + Test 数据
    - raw_data: 原始数据字符串

# 串口管理
class SerialManager:
    - 串口连接管理
    - 数据接收处理
    - 模拟数据生成

# 数据解析
class DataParser:
    - 原始数据解析
    - 数据验证
    - 统计信息

# 专业GUI界面
class ProfessionalVisualizerGUI:
    - 3D可视化
    - 传感器面板
    - 控制面板
```

### 2. 依赖库

```python
# 核心依赖
import tkinter as tk          # GUI框架
import matplotlib.pyplot      # 3D绘图
import numpy as np           # 数值计算
import serial                # 串口通信 (可选)

# 标准库
import threading, time, csv, configparser, logging
```

## 使用说明

### 1. 启动软件

```bash
# 直接启动
python SerialDataVisualizer_Professional.py

# 指定参数启动
python SerialDataVisualizer_Professional.py --port COM3 --baudrate 250000
```

### 2. 界面操作

1. **连接设置**: 选择端口和波特率
2. **连接控制**: 点击"连接"按钮开始数据接收
3. **数据记录**: 点击"开始记录"保存数据
4. **数据导出**: 点击"导出数据"保存为CSV文件

### 3. 数据格式

#### 输入数据格式
```
1333,638,1331,1271,1327,629,1241,631,1259,629
```

#### CSV导出格式
```csv
timestamp,Normal 1,Normal 2,Normal 3,Normal 4,Normal 5,Spear 1,Spear 2,Spear 3,Spear 4,Test,raw_data
2025-08-26T15:30:45.123456,1333.0,638.0,1331.0,1271.0,1327.0,629.0,1241.0,631.0,1259.0,629.0,"1333,638,1331,1271,1327,629,1241,631,1259,629"
```

## 测试验证

### 1. 自动化测试

运行测试脚本验证所有功能：
```bash
python test_professional_visualizer.py
```

### 2. 测试覆盖

- ✓ 模块导入测试
- ✓ 数据结构测试  
- ✓ 串口管理测试
- ✓ 数据解析测试
- ✓ 传感器配置测试
- ✓ 参考图一致性验证
- ✓ GUI组件创建测试

## 与参考图的对比

### 完全匹配项目
- ✅ 界面布局和标题
- ✅ 传感器位置坐标
- ✅ 传感器标签名称
- ✅ 数据显示格式
- ✅ 颜色分类方案
- ✅ 实时时间显示
- ✅ 3D坐标图样式
- ✅ 数据数值范围

### 增强功能
- 🔧 串口连接管理
- 🔧 数据记录和导出
- 🔧 错误处理和日志
- 🔧 配置参数管理
- 🔧 自动化测试支持

## 性能特性

- **内存使用**: 低内存占用，适合长时间运行
- **CPU使用**: 优化的更新频率，CPU占用率低
- **数据处理**: 支持高频数据接收和处理
- **界面响应**: 流畅的实时更新，无卡顿

## 扩展性

- **传感器数量**: 可轻松扩展支持更多传感器
- **数据格式**: 支持多种数据分隔符
- **可视化**: 可添加更多图表类型
- **导出格式**: 可支持更多导出格式

## 维护说明

- **日志文件**: `serial_visualizer.log`
- **配置管理**: 支持配置文件保存设置
- **错误处理**: 完善的异常处理机制
- **代码结构**: 模块化设计，易于维护

---

**注意**: 此软件完全按照用户提供的参考图进行设计和实现，确保界面布局、数据显示和功能特性与参考图保持一致。
