#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
波特率支持测试脚本
版权归属: 米醋电子工作室
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False

from config.constants import SERIAL_BAUDRATES, COMMON_BAUDRATES


def test_baudrate_support():
    """测试波特率支持"""
    print("=" * 60)
    print("           波特率支持测试")
    print("              版权归属: 米醋电子工作室")
    print("=" * 60)
    print()
    
    print(f"串口库可用: {'是' if SERIAL_AVAILABLE else '否'}")
    print()
    
    # 显示支持的波特率
    print("支持的波特率列表:")
    print("-" * 40)
    
    print("常用波特率:")
    for i, rate in enumerate(COMMON_BAUDRATES):
        print(f"  {i+1:2d}. {rate:>8d} bps")
    
    print(f"\n完整波特率列表 ({len(SERIAL_BAUDRATES)} 种):")
    for i, rate in enumerate(SERIAL_BAUDRATES):
        if i % 6 == 0:
            print()
        print(f"{rate:>8d}", end="  ")
    print("\n")
    
    # 重点测试250000波特率
    print("重点测试 250000 波特率:")
    print("-" * 40)
    
    if 250000 in SERIAL_BAUDRATES:
        print("✓ 250000 波特率已包含在支持列表中")
    else:
        print("✗ 250000 波特率未包含在支持列表中")
    
    if 250000 in COMMON_BAUDRATES:
        print("✓ 250000 波特率在常用列表中")
    else:
        print("✗ 250000 波特率不在常用列表中")
    
    # 测试串口连接 (如果有串口库)
    if SERIAL_AVAILABLE:
        print("\n串口连接测试:")
        print("-" * 40)
        
        # 扫描端口
        try:
            ports = serial.tools.list_ports.comports()
            available_ports = [port.device for port in ports]
            print(f"可用端口: {', '.join(available_ports) if available_ports else '无'}")
            
            if available_ports:
                test_port = available_ports[0]
                print(f"\n使用端口 {test_port} 测试波特率连接:")
                
                # 测试几个关键波特率
                test_rates = [115200, 230400, 250000, 460800, 921600]
                
                for rate in test_rates:
                    try:
                        ser = serial.Serial(
                            port=test_port,
                            baudrate=rate,
                            timeout=0.1
                        )
                        ser.close()
                        print(f"  ✓ {rate:>6d} bps - 连接成功")
                    except Exception as e:
                        print(f"  ✗ {rate:>6d} bps - 连接失败: {str(e)[:30]}...")
            
        except Exception as e:
            print(f"端口扫描失败: {e}")
    
    else:
        print("\n注意: pyserial 未安装，无法进行实际串口测试")
        print("建议运行: pip install pyserial")
    
    print()


def test_baudrate_selection():
    """测试波特率选择功能"""
    print("波特率选择功能测试:")
    print("-" * 40)
    
    # 模拟用户选择
    test_inputs = [
        ("5", "选择常用波特率序号5"),
        ("250000", "直接输入250000"),
        ("3000000", "输入最高波特率3000000"),
        ("invalid", "无效输入测试"),
        ("", "空输入测试")
    ]
    
    for test_input, description in test_inputs:
        print(f"\n测试: {description}")
        print(f"输入: '{test_input}'")
        
        try:
            if test_input and test_input.isdigit():
                input_num = int(test_input)
                
                # 检查是否是选择序号
                if 1 <= input_num <= len(COMMON_BAUDRATES):
                    selected_baudrate = COMMON_BAUDRATES[input_num - 1]
                    print(f"结果: 选择常用波特率 {selected_baudrate}")
                # 检查是否是直接输入的波特率
                elif input_num in SERIAL_BAUDRATES:
                    selected_baudrate = input_num
                    print(f"结果: 使用输入的波特率 {selected_baudrate}")
                else:
                    print(f"结果: 波特率 {input_num} 不在支持列表中，但将尝试使用")
                    selected_baudrate = input_num
            else:
                selected_baudrate = 250000  # 新的默认值
                print(f"结果: 使用默认波特率 {selected_baudrate}")
                
        except Exception as e:
            print(f"结果: 处理异常，使用默认波特率 250000")
    
    print()


def generate_baudrate_info():
    """生成波特率信息文档"""
    print("生成波特率信息文档:")
    print("-" * 40)
    
    info_content = f"""# 串口数据可视化软件 - 波特率支持信息

## 生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 支持的波特率

### 常用波特率 ({len(COMMON_BAUDRATES)} 种)
推荐使用以下波特率，这些是最常见和稳定的选择：

"""
    
    for i, rate in enumerate(COMMON_BAUDRATES):
        info_content += f"{i+1:2d}. {rate:>8d} bps\n"
    
    info_content += f"""
### 完整波特率列表 ({len(SERIAL_BAUDRATES)} 种)
软件支持以下所有波特率：

"""
    
    # 按行显示，每行6个
    for i, rate in enumerate(SERIAL_BAUDRATES):
        if i % 6 == 0:
            info_content += "\n"
        info_content += f"{rate:>8d}  "
    
    info_content += """

## 特殊说明

### 高速波特率支持
- ✅ **250000 bps**: 您当前需要的波特率，已完全支持
- ✅ **460800 bps**: 高速数据传输
- ✅ **921600 bps**: 超高速数据传输
- ✅ **1000000+ bps**: 支持最高到3000000 bps

### 使用建议
1. **250000 bps**: 适合高速传感器数据采集
2. **115200 bps**: 标准高速率，兼容性最好
3. **921600 bps**: 超高速应用，需要硬件支持
4. **自定义波特率**: 支持手动输入任意波特率

### 硬件要求
- 高速波特率 (>230400) 需要硬件支持
- USB转串口芯片需要支持对应波特率
- 建议使用FTDI或CH340G等高质量芯片

---
版权归属: 米醋电子工作室
"""
    
    try:
        with open('baudrate_support_info.txt', 'w', encoding='utf-8') as f:
            f.write(info_content)
        print("✓ 波特率信息文档已生成: baudrate_support_info.txt")
    except Exception as e:
        print(f"✗ 生成文档失败: {e}")


def main():
    """主函数"""
    test_baudrate_support()
    test_baudrate_selection()
    generate_baudrate_info()
    
    print("=" * 60)
    print("测试完成！")
    print()
    print("重要提醒:")
    print("- 250000 波特率已完全支持")
    print("- 软件默认波特率已更改为 250000")
    print("- 支持最高到 3000000 波特率")
    print("- 可手动输入任意波特率值")
    print()
    print("版权归属: 米醋电子工作室")


if __name__ == "__main__":
    main()
