#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口数据可视化软件演示脚本
版权归属: 米醋电子工作室
"""

import sys
import os
import time
import random
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.constants import SENSOR_POSITIONS
from utils.logger import setup_logger
from utils.file_handler import DataFileHandler


def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')


def generate_demo_data():
    """生成演示数据"""
    base_values = [1000, 1500, 2000, 2500, 3000]
    return [base + random.randint(-300, 300) for base in base_values]


def display_sensor_data(data):
    """显示传感器数据"""
    print("=" * 60)
    print("           串口数据可视化软件 - 演示版本")
    print("              版权归属: 米醋电子工作室")
    print("=" * 60)
    print()
    
    current_time = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"时间戳: {current_time}")
    print()
    
    print("实时传感器数据:")
    print("-" * 40)
    
    for i, (value, pos) in enumerate(zip(data, SENSOR_POSITIONS)):
        # 根据数值选择显示符号
        if value < 1000:
            symbol = "●"  # 低值
            color = "\033[92m"  # 绿色
        elif value < 2000:
            symbol = "◐"  # 中值
            color = "\033[93m"  # 黄色
        elif value < 3000:
            symbol = "◑"  # 高值
            color = "\033[95m"  # 紫色
        else:
            symbol = "○"  # 很高值
            color = "\033[91m"  # 红色
        
        print(f"传感器{i+1} 位置[{pos[0]:.1f}, {pos[1]:.1f}]: {color}{symbol} {value:7.2f}\033[0m")
    
    print()


def display_3d_ascii(data):
    """显示ASCII 3D可视化"""
    print("3D可视化 (俯视图):")
    print("-" * 40)
    
    # 创建网格
    grid = [[' ' for _ in range(25)] for _ in range(13)]
    
    # 绘制边框和网格线
    for i in range(13):
        grid[i][0] = '|'
        grid[i][24] = '|'
    for j in range(25):
        grid[0][j] = '-'
        grid[12][j] = '-'
    
    # 绘制内部网格点
    for i in range(2, 11, 2):
        for j in range(4, 21, 4):
            grid[i][j] = '·'
    
    # 绘制传感器
    for i, (value, pos) in enumerate(zip(data, SENSOR_POSITIONS)):
        x = int(pos[0] * 20) + 2
        y = int(pos[1] * 8) + 2
        
        # 根据数值选择字符
        if value > 2500:
            char = '●'  # 高值
        elif value > 1500:
            char = '◐'  # 中值
        else:
            char = '○'  # 低值
        
        if 0 <= y < 13 and 0 <= x < 25:
            grid[y][x] = char
    
    # 输出网格
    for row in grid:
        print(''.join(row))
    
    print()
    print("图例: ○ 低值 (<1500)  ◐ 中值 (1500-2500)  ● 高值 (>2500)")
    print()


def display_statistics(total_count, demo_time):
    """显示统计信息"""
    print("统计信息:")
    print("-" * 40)
    print(f"数据包总数: {total_count}")
    print(f"运行时间: {demo_time:.1f} 秒")
    print(f"数据更新率: {total_count/demo_time:.1f} Hz")
    print()


def main():
    """主演示函数"""
    print("启动串口数据可视化软件演示...")
    time.sleep(1)
    
    # 初始化
    logger = setup_logger()
    file_handler = DataFileHandler()
    
    demo_data = []
    start_time = time.time()
    count = 0
    
    try:
        print("演示将运行10秒，按Ctrl+C可提前退出...")
        time.sleep(2)
        
        while time.time() - start_time < 10:  # 运行10秒
            # 生成新数据
            current_data = generate_demo_data()
            count += 1
            
            # 记录数据
            demo_data.append({
                'timestamp': datetime.now().isoformat(),
                'sensor1': current_data[0],
                'sensor2': current_data[1],
                'sensor3': current_data[2],
                'sensor4': current_data[3],
                'sensor5': current_data[4],
            })
            
            # 清屏并显示
            clear_screen()
            display_sensor_data(current_data)
            display_3d_ascii(current_data)
            display_statistics(count, time.time() - start_time)
            
            print("演示中... (Ctrl+C 退出)")
            
            time.sleep(0.5)  # 2Hz更新率
    
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    
    # 演示结束
    clear_screen()
    print("=" * 60)
    print("                    演示完成!")
    print("=" * 60)
    print()
    
    elapsed_time = time.time() - start_time
    print(f"演示时长: {elapsed_time:.1f} 秒")
    print(f"生成数据: {count} 组")
    print(f"平均更新率: {count/elapsed_time:.1f} Hz")
    print()
    
    # 导出演示数据
    if demo_data:
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"demo_data_{timestamp}.csv"
            filepath = file_handler.save_to_csv(demo_data, filename)
            print(f"演示数据已保存到: {filepath}")
            print(f"数据记录数: {len(demo_data)} 条")
        except Exception as e:
            print(f"保存演示数据失败: {e}")
    
    print()
    print("感谢使用串口数据可视化软件!")
    print("版权归属: 米醋电子工作室")


if __name__ == "__main__":
    main()
