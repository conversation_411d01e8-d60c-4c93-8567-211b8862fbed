#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据控制器模块
版权归属: 米醋电子工作室
"""

from collections import deque
from datetime import datetime
from typing import List, Optional, Dict, Any
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from core.serial_manager import SerialManager
from core.data_parser import DataParser, SensorData
from utils.logger import get_logger
from utils.file_handler import DataFileHandler
from config.constants import PERFORMANCE


class DataController(QObject):
    """数据流控制器"""
    
    # 信号定义
    new_data_available = pyqtSignal(object)  # 新数据可用信号
    data_buffer_updated = pyqtSignal(list)  # 数据缓冲区更新信号
    recording_status_changed = pyqtSignal(bool)  # 记录状态变化信号
    export_completed = pyqtSignal(str)  # 导出完成信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger()
        
        # 初始化组件
        self.serial_manager = SerialManager()
        self.data_parser = DataParser()
        self.file_handler = DataFileHandler()
        
        # 数据缓冲区
        self.data_buffer = deque(maxlen=PERFORMANCE['DATA_BUFFER_SIZE'])
        self.recording_buffer = []
        
        # 状态管理
        self.is_recording = False
        self.is_paused = False
        self.last_data_time = None
        
        # 自动保存定时器
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self._auto_save_data)
        
        # 连接信号
        self._connect_signals()
        
        self.logger.info("数据控制器初始化完成")
    
    def _connect_signals(self):
        """连接内部信号"""
        # 串口数据接收
        self.serial_manager.data_received.connect(self._on_data_received)
        self.serial_manager.connection_changed.connect(self._on_connection_changed)
        self.serial_manager.error_occurred.connect(self._on_serial_error)
        
        # 数据解析
        self.data_parser.sensor_data_ready.connect(self._on_sensor_data_ready)
        self.data_parser.parse_error.connect(self._on_parse_error)
    
    def _on_data_received(self, raw_data: bytes):
        """处理接收到的原始数据"""
        if not self.is_paused:
            self.data_parser.parse_data(raw_data)
    
    def _on_sensor_data_ready(self, sensor_data: SensorData):
        """处理解析完成的传感器数据"""
        # 添加到缓冲区
        self.data_buffer.append(sensor_data)
        
        # 记录数据
        if self.is_recording:
            self.recording_buffer.append(sensor_data)
        
        # 更新最后数据时间
        self.last_data_time = datetime.now()
        
        # 发送信号
        self.new_data_available.emit(sensor_data)
        self.data_buffer_updated.emit(list(self.data_buffer))
        
        self.logger.debug(f"新数据已处理: {sensor_data.sensor_values}")
    
    def _on_connection_changed(self, connected: bool):
        """处理连接状态变化"""
        if connected:
            self.logger.info("串口连接已建立")
        else:
            self.logger.info("串口连接已断开")
            # 连接断开时停止记录
            if self.is_recording:
                self.stop_recording()
    
    def _on_serial_error(self, error_msg: str):
        """处理串口错误"""
        self.logger.error(f"串口错误: {error_msg}")
    
    def _on_parse_error(self, error_msg: str):
        """处理解析错误"""
        self.logger.error(f"数据解析错误: {error_msg}")
    
    # 串口控制方法
    def connect_serial(self, port: str, baudrate: int) -> bool:
        """连接串口"""
        self.serial_manager.set_connection_params(port, baudrate)
        return self.serial_manager.connect()
    
    def disconnect_serial(self):
        """断开串口连接"""
        self.serial_manager.disconnect()
    
    def get_available_ports(self) -> List[str]:
        """获取可用串口列表"""
        return self.serial_manager.scan_ports()
    
    def is_serial_connected(self) -> bool:
        """检查串口是否已连接"""
        return self.serial_manager.is_connected
    
    # 数据控制方法
    def pause_data_processing(self):
        """暂停数据处理"""
        self.is_paused = True
        self.logger.info("数据处理已暂停")
    
    def resume_data_processing(self):
        """恢复数据处理"""
        self.is_paused = False
        self.logger.info("数据处理已恢复")
    
    def clear_data_buffer(self):
        """清空数据缓冲区"""
        self.data_buffer.clear()
        self.data_buffer_updated.emit([])
        self.logger.info("数据缓冲区已清空")
    
    def get_latest_data(self) -> Optional[SensorData]:
        """获取最新数据"""
        if self.data_buffer:
            return self.data_buffer[-1]
        return None
    
    def get_data_buffer(self) -> List[SensorData]:
        """获取数据缓冲区内容"""
        return list(self.data_buffer)
    
    # 数据记录方法
    def start_recording(self):
        """开始数据记录"""
        if not self.is_recording:
            self.is_recording = True
            self.recording_buffer.clear()
            self.recording_status_changed.emit(True)
            self.logger.info("开始数据记录")
    
    def stop_recording(self):
        """停止数据记录"""
        if self.is_recording:
            self.is_recording = False
            self.recording_status_changed.emit(False)
            self.logger.info(f"停止数据记录，共记录 {len(self.recording_buffer)} 条数据")
    
    def export_recorded_data(self, filename: str = None) -> str:
        """导出记录的数据"""
        if not self.recording_buffer:
            raise ValueError("没有记录的数据可导出")
        
        # 转换数据格式
        export_data = []
        for sensor_data in self.recording_buffer:
            row = {
                'timestamp': sensor_data.timestamp.isoformat(),
                'sensor1': sensor_data.sensor_values[0],
                'sensor2': sensor_data.sensor_values[1],
                'sensor3': sensor_data.sensor_values[2],
                'sensor4': sensor_data.sensor_values[3],
                'sensor5': sensor_data.sensor_values[4],
                'ext1': sensor_data.extended_data[0],
                'ext2': sensor_data.extended_data[1],
                'ext3': sensor_data.extended_data[2],
                'ext4': sensor_data.extended_data[3],
                'ext5': sensor_data.extended_data[4],
                'raw_data': sensor_data.raw_data
            }
            export_data.append(row)
        
        # 保存到文件
        filepath = self.file_handler.save_to_csv(export_data, filename)
        self.export_completed.emit(filepath)
        return filepath
    
    def export_buffer_data(self, filename: str = None) -> str:
        """导出缓冲区数据"""
        if not self.data_buffer:
            raise ValueError("缓冲区没有数据可导出")
        
        # 转换数据格式
        export_data = []
        for sensor_data in self.data_buffer:
            row = {
                'timestamp': sensor_data.timestamp.isoformat(),
                'sensor1': sensor_data.sensor_values[0],
                'sensor2': sensor_data.sensor_values[1],
                'sensor3': sensor_data.sensor_values[2],
                'sensor4': sensor_data.sensor_values[3],
                'sensor5': sensor_data.sensor_values[4],
                'ext1': sensor_data.extended_data[0],
                'ext2': sensor_data.extended_data[1],
                'ext3': sensor_data.extended_data[2],
                'ext4': sensor_data.extended_data[3],
                'ext5': sensor_data.extended_data[4],
                'raw_data': sensor_data.raw_data
            }
            export_data.append(row)
        
        # 保存到文件
        filepath = self.file_handler.save_to_csv(export_data, filename)
        self.export_completed.emit(filepath)
        return filepath
    
    def _auto_save_data(self):
        """自动保存数据"""
        if self.is_recording and self.recording_buffer:
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"auto_save_{timestamp}.csv"
                self.export_recorded_data(filename)
                self.logger.info(f"数据已自动保存: {filename}")
            except Exception as e:
                self.logger.error(f"自动保存失败: {e}")
    
    def enable_auto_save(self, interval_minutes: int = 5):
        """启用自动保存"""
        self.auto_save_timer.start(interval_minutes * 60 * 1000)
        self.logger.info(f"自动保存已启用，间隔: {interval_minutes} 分钟")
    
    def disable_auto_save(self):
        """禁用自动保存"""
        self.auto_save_timer.stop()
        self.logger.info("自动保存已禁用")
    
    def get_status(self) -> Dict[str, Any]:
        """获取控制器状态"""
        return {
            'serial_connected': self.is_serial_connected(),
            'is_recording': self.is_recording,
            'is_paused': self.is_paused,
            'buffer_size': len(self.data_buffer),
            'recorded_count': len(self.recording_buffer),
            'last_data_time': self.last_data_time.isoformat() if self.last_data_time else None,
            'parser_stats': self.data_parser.get_statistics()
        }
