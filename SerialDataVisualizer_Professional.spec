# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['SerialDataVisualizer_Professional.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.ini', '.'),
        ('docs', 'docs'),
        ('使用说明.md', '.'),
        ('README_Professional.md', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'matplotlib',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'matplotlib.animation',
        'mpl_toolkits.mplot3d',
        'numpy',
        'serial',
        'serial.tools.list_ports',
        'csv',
        'configparser',
        'logging',
        'logging.handlers',
        'threading',
        'datetime',
        'argparse',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyd = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyd,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SerialDataVisualizer_Professional',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
