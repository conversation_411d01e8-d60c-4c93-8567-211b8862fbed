#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据解析器模块
版权归属: 米醋电子工作室
"""

import re
from datetime import datetime
from typing import List, Optional, Tuple
from dataclasses import dataclass
from PyQt5.QtCore import QObject, pyqtSignal
from utils.logger import get_logger
from config.constants import DATA_PACKET_SIZE, SENSOR_COUNT, SENSOR_POSITIONS


@dataclass
class SensorData:
    """传感器数据结构"""
    timestamp: datetime
    sensor_values: List[float]  # 5个传感器数值
    extended_data: List[float]  # 5个扩展数据
    positions: List[Tuple[float, float]]  # 传感器位置坐标
    raw_data: str  # 原始数据字符串


class DataParser(QObject):
    """数据解析和处理器"""
    
    # 信号定义
    sensor_data_ready = pyqtSignal(object)  # 传感器数据就绪信号
    parse_error = pyqtSignal(str)  # 解析错误信号
    data_statistics = pyqtSignal(dict)  # 数据统计信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger()
        
        # 数据统计
        self.total_packets = 0
        self.valid_packets = 0
        self.error_packets = 0
        
        # 数据验证范围
        self.min_value = 0
        self.max_value = 4000
        
        # 数据分隔符模式
        self.delimiter_patterns = [
            r'[,\s]+',  # 逗号或空格分隔
            r'[;\s]+',  # 分号或空格分隔
            r'\s+',     # 仅空格分隔
        ]
        
        self.logger.info("数据解析器初始化完成")
    
    def set_value_range(self, min_val: float, max_val: float):
        """设置数据值范围"""
        self.min_value = min_val
        self.max_value = max_val
        self.logger.info(f"数据范围已设置: {min_val} - {max_val}")
    
    def parse_data(self, raw_data: bytes) -> Optional[SensorData]:
        """解析原始数据"""
        self.total_packets += 1
        
        try:
            # 转换为字符串
            data_str = raw_data.decode('utf-8', errors='ignore').strip()
            if not data_str:
                return None
            
            # 尝试不同的分隔符模式
            values = None
            for pattern in self.delimiter_patterns:
                try:
                    parts = re.split(pattern, data_str)
                    # 过滤空字符串
                    parts = [part for part in parts if part]
                    
                    if len(parts) >= DATA_PACKET_SIZE:
                        values = [float(part) for part in parts[:DATA_PACKET_SIZE]]
                        break
                except (ValueError, IndexError):
                    continue
            
            if values is None:
                raise ValueError(f"无法解析数据格式: {data_str}")
            
            # 验证数据包大小
            if len(values) < DATA_PACKET_SIZE:
                raise ValueError(f"数据包大小不足: 期望{DATA_PACKET_SIZE}个，实际{len(values)}个")
            
            # 分离传感器数据和扩展数据
            sensor_values = values[:SENSOR_COUNT]
            extended_data = values[SENSOR_COUNT:DATA_PACKET_SIZE]
            
            # 数据范围验证
            for i, value in enumerate(sensor_values):
                if not (self.min_value <= value <= self.max_value):
                    self.logger.warning(f"传感器{i+1}数值超出范围: {value}")
            
            # 创建传感器数据对象
            sensor_data = SensorData(
                timestamp=datetime.now(),
                sensor_values=sensor_values,
                extended_data=extended_data,
                positions=SENSOR_POSITIONS.copy(),
                raw_data=data_str
            )
            
            self.valid_packets += 1
            
            # 发送数据就绪信号
            self.sensor_data_ready.emit(sensor_data)
            
            # 更新统计信息
            self._update_statistics()
            
            self.logger.debug(f"数据解析成功: {sensor_values}")
            return sensor_data
            
        except Exception as e:
            self.error_packets += 1
            error_msg = f"数据解析失败: {e}"
            self.logger.error(error_msg)
            self.parse_error.emit(error_msg)
            self._update_statistics()
            return None
    
    def _update_statistics(self):
        """更新数据统计"""
        if self.total_packets > 0:
            success_rate = (self.valid_packets / self.total_packets) * 100
        else:
            success_rate = 0
        
        stats = {
            'total_packets': self.total_packets,
            'valid_packets': self.valid_packets,
            'error_packets': self.error_packets,
            'success_rate': success_rate
        }
        
        self.data_statistics.emit(stats)
    
    def validate_sensor_data(self, sensor_data: SensorData) -> bool:
        """验证传感器数据的有效性"""
        try:
            # 检查数据完整性
            if len(sensor_data.sensor_values) != SENSOR_COUNT:
                return False
            
            if len(sensor_data.extended_data) != SENSOR_COUNT:
                return False
            
            # 检查数值范围
            for value in sensor_data.sensor_values:
                if not isinstance(value, (int, float)):
                    return False
                if not (self.min_value <= value <= self.max_value):
                    return False
            
            # 检查时间戳
            if not isinstance(sensor_data.timestamp, datetime):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {e}")
            return False
    
    def get_statistics(self) -> dict:
        """获取解析统计信息"""
        if self.total_packets > 0:
            success_rate = (self.valid_packets / self.total_packets) * 100
            error_rate = (self.error_packets / self.total_packets) * 100
        else:
            success_rate = 0
            error_rate = 0
        
        return {
            'total_packets': self.total_packets,
            'valid_packets': self.valid_packets,
            'error_packets': self.error_packets,
            'success_rate': round(success_rate, 2),
            'error_rate': round(error_rate, 2)
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.total_packets = 0
        self.valid_packets = 0
        self.error_packets = 0
        self.logger.info("数据统计已重置")
        self._update_statistics()
    
    def format_data_for_display(self, sensor_data: SensorData) -> dict:
        """格式化数据用于显示"""
        return {
            'timestamp': sensor_data.timestamp.strftime('%H:%M:%S.%f')[:-3],
            'sensor1': f"{sensor_data.sensor_values[0]:.2f}",
            'sensor2': f"{sensor_data.sensor_values[1]:.2f}",
            'sensor3': f"{sensor_data.sensor_values[2]:.2f}",
            'sensor4': f"{sensor_data.sensor_values[3]:.2f}",
            'sensor5': f"{sensor_data.sensor_values[4]:.2f}",
            'ext1': f"{sensor_data.extended_data[0]:.2f}",
            'ext2': f"{sensor_data.extended_data[1]:.2f}",
            'ext3': f"{sensor_data.extended_data[2]:.2f}",
            'ext4': f"{sensor_data.extended_data[3]:.2f}",
            'ext5': f"{sensor_data.extended_data[4]:.2f}",
        }
