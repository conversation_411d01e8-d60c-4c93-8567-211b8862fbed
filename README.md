# 串口数据可视化软件 v1.0

## 📋 项目简介

串口数据可视化软件是一个专业的实时数据监控工具，用于接收单片机通过串口发送的传感器数据，并进行直观的3D可视化展示。

**版权归属**: 米醋电子工作室  
**版本**: v1.0.0  
**开发日期**: 2025-08-26

## ✨ 功能特点

- 🔌 **实时串口通信**: 支持多种波特率，自动端口检测
- 📊 **3D数据可视化**: 直观的传感器数据3D显示
- 💾 **数据记录导出**: 支持CSV格式数据导出
- 🎛️ **用户友好界面**: 简洁直观的操作界面
- 🔄 **实时数据更新**: 高频率数据刷新显示
- 📈 **统计信息**: 详细的数据统计和状态监控
- 🎯 **独立运行**: 无需安装Python环境

## 🚀 快速开始

### 方式一: 运行可执行文件 (推荐)
1. 下载发布版本的压缩包
2. 解压到任意目录
3. 双击 `启动程序.bat` 或直接运行 `SerialDataVisualizer.exe`

### 方式二: 运行Python脚本
```bash
# 安装依赖
pip install -r requirements.txt

# 运行主程序 (GUI版本)
python main.py

# 运行命令行版本
python serial_visualizer_cli.py

# 运行演示版本
python demo.py
```

## 📊 数据格式

### 输入数据格式
单片机发送的数据应为10个数值一组，支持多种分隔符：

```
# 逗号分隔
1000,1500,2000,2500,3000,100,200,300,400,500

# 空格分隔  
1000 1500 2000 2500 3000 100 200 300 400 500

# 分号分隔
1000;1500;2000;2500;3000;100;200;300;400;500
```

### 数据含义
- **前5个数值**: 传感器读数 (用于3D可视化)
- **后5个数值**: 扩展数据 (预留使用)

### 传感器位置映射
```
传感器1: (0.5, 0.5) - 中心位置
传感器2: (1.0, 1.0) - 右上角  
传感器3: (1.0, 0.0) - 右下角
传感器4: (0.0, 1.0) - 左上角
传感器5: (0.0, 0.0) - 左下角
```

## 🎮 使用说明

### 1. 连接设置
- 选择正确的串口端口 (如: COM1, COM2)
- 设置合适的波特率 (默认: 115200)
- 点击"连接"按钮建立连接

### 2. 数据监控
- 连接成功后，软件会自动接收和显示数据
- 右侧面板显示实时数值
- 左侧3D视图显示传感器位置和数值

### 3. 3D视图操作
- **鼠标拖拽**: 旋转视角
- **滚轮**: 缩放视图
- **重置视图**: 恢复默认视角

### 4. 数据记录
- 点击"开始记录"开始数据记录
- 点击"停止记录"结束记录
- 点击"导出数据"保存为CSV文件

### 5. 显示选项
- 网格显示: 显示/隐藏坐标网格
- 坐标轴: 显示/隐藏坐标轴
- 标签: 显示/隐藏数值标签

## 📁 文件结构

```
串口数据可视化软件/
├── main.py                 # 主程序入口 (GUI版本)
├── serial_visualizer_cli.py # 命令行版本
├── demo.py                 # 演示程序
├── requirements.txt        # 依赖包列表
├── config.ini             # 配置文件
├── README.md              # 使用说明
├── config/                # 配置模块
│   ├── settings.py        # 配置管理
│   └── constants.py       # 常量定义
├── core/                  # 核心业务逻辑
│   ├── serial_manager.py  # 串口管理
│   ├── data_parser.py     # 数据解析
│   └── data_controller.py # 数据控制
├── ui/                    # 用户界面
│   ├── main_window.py     # 主窗口
│   ├── opengl_widget.py   # 3D可视化
│   └── control_panel.py   # 控制面板
├── utils/                 # 工具模块
│   ├── logger.py          # 日志系统
│   └── file_handler.py    # 文件处理
├── docs/                  # 文档
│   ├── prd/              # 产品需求文档
│   ├── architecture/     # 架构设计文档
│   ├── development/      # 开发技术文档
│   └── tasks/           # 任务规划文档
├── tests/                # 测试文件
├── exports/              # 数据导出目录
├── data/                 # 数据存储目录
└── logs/                 # 日志文件目录
```

## ⚙️ 配置选项

### 串口配置
```ini
[SERIAL]
port = COM1           # 串口端口
baudrate = 115200     # 波特率
databits = 8          # 数据位
stopbits = 1          # 停止位
parity = N            # 校验位
timeout = 1.0         # 超时时间
auto_reconnect = True # 自动重连
```

### 显示配置
```ini
[DISPLAY]
window_width = 1200   # 窗口宽度
window_height = 800   # 窗口高度
fps_limit = 60        # 帧率限制
show_grid = True      # 显示网格
show_axes = True      # 显示坐标轴
```

### 数据配置
```ini
[DATA]
buffer_size = 1000    # 缓冲区大小
auto_save = False     # 自动保存
save_interval = 60    # 保存间隔(秒)
data_format = csv     # 数据格式
```

## 🔧 命令行参数

```bash
# 查看帮助
python serial_visualizer_cli.py --help

# 指定端口和波特率
python serial_visualizer_cli.py --port COM3 --baudrate 9600

# 使用默认设置
python serial_visualizer_cli.py
```

## 📈 性能指标

- **支持波特率**: 9600 - 115200 bps
- **数据更新率**: 最高100Hz
- **3D渲染帧率**: 30-60 fps
- **内存使用**: < 100MB
- **CPU使用率**: < 20%
- **连接成功率**: > 99%

## 🛠️ 系统要求

### 最低要求
- **操作系统**: Windows 10/11 64位
- **内存**: 4GB RAM
- **存储空间**: 100MB 可用空间
- **显卡**: 支持OpenGL 3.3的显卡

### 推荐配置
- **操作系统**: Windows 11 64位
- **内存**: 8GB RAM
- **存储空间**: 500MB 可用空间
- **显卡**: 独立显卡

## 🐛 故障排除

### 常见问题

**Q: 无法连接串口**
A: 检查端口是否被其他程序占用，确认设备管理器中端口状态正常

**Q: 数据解析失败**
A: 确认数据格式正确，检查分隔符是否为逗号、空格或分号

**Q: 3D显示异常**
A: 更新显卡驱动，确保支持OpenGL 3.3

**Q: 程序运行缓慢**
A: 降低数据更新频率，减少缓冲区大小

### 日志文件
程序运行时会生成日志文件 `serial_visualizer.log`，包含详细的运行信息和错误记录。

## 📞 技术支持

如遇到问题或需要技术支持，请联系:

**米醋电子工作室**
- 项目地址: [GitHub Repository]
- 技术文档: `docs/` 目录
- 问题反馈: 通过GitHub Issues

## 📄 许可证

本软件版权归属于米醋电子工作室，仅供学习和研究使用。

## 🔄 版本历史

### v1.0.0 (2025-08-26)
- ✨ 初始版本发布
- 🔌 实现串口数据接收功能
- 📊 实现3D数据可视化
- 💾 实现数据记录和导出
- 🎛️ 实现用户界面
- 📦 支持独立可执行文件

---

**感谢使用串口数据可视化软件！**  
**版权归属: 米醋电子工作室**
