#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试exe文件是否正常工作
"""

import os
import subprocess
import time

def test_exe():
    """测试exe文件"""
    exe_path = "SerialDataVisualizer_Professional_Release_20250826_164039\\SerialDataVisualizer_Professional.exe"
    
    if not os.path.exists(exe_path):
        print(f"✗ 找不到exe文件: {exe_path}")
        return False
    
    print(f"✓ 找到exe文件: {exe_path}")
    print(f"文件大小: {os.path.getsize(exe_path) / 1024 / 1024:.1f} MB")
    
    # 测试exe文件是否能启动
    try:
        print("正在测试exe文件启动...")
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # 等待2秒看是否能正常启动
        time.sleep(2)
        
        if process.poll() is None:
            print("✓ exe文件启动成功")
            process.terminate()
            process.wait()
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"✗ exe文件启动失败")
            if stdout:
                print(f"stdout: {stdout.decode('utf-8', errors='ignore')}")
            if stderr:
                print(f"stderr: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"✗ 测试exe文件时出错: {e}")
        return False

if __name__ == "__main__":
    test_exe()
