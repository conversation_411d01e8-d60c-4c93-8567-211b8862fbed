#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业版串口数据可视化软件打包脚本
版权归属: 米醋电子工作室
创建日期: 2025-08-26
作者: <PERSON> (工程师)

专门为专业版本打包exe文件
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime
import zipfile

def check_pyinstaller():
    """检查PyInstaller是否安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller 已安装 (版本: {PyInstaller.__version__})")
        return True
    except ImportError:
        print("✗ PyInstaller 未安装")
        return False

def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装 PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ PyInstaller 安装失败: {e}")
        return False

def create_spec_file():
    """创建专业版的spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['SerialDataVisualizer_Professional.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.ini', '.'),
        ('docs', 'docs'),
        ('使用说明.md', '.'),
        ('README_Professional.md', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'matplotlib',
        'matplotlib.backends.backend_tkagg',
        'matplotlib.figure',
        'matplotlib.animation',
        'mpl_toolkits.mplot3d',
        'numpy',
        'serial',
        'serial.tools.list_ports',
        'csv',
        'configparser',
        'logging',
        'logging.handlers',
        'threading',
        'datetime',
        'argparse',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyd = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyd,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SerialDataVisualizer_Professional',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
'''
    
    with open('SerialDataVisualizer_Professional.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ 专业版spec文件创建成功")

def build_executable():
    """构建可执行文件"""
    print("开始构建专业版可执行文件...")
    
    try:
        # 使用spec文件构建
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "SerialDataVisualizer_Professional.spec"]
        
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ 可执行文件构建成功")
            return True
        else:
            print(f"✗ 构建失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ 构建过程出错: {e}")
        return False

def create_release_package():
    """创建发布包"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    release_dir = f"SerialDataVisualizer_Professional_Release_{timestamp}"
    
    print(f"创建发布包: {release_dir}")
    
    try:
        # 创建发布目录
        if os.path.exists(release_dir):
            shutil.rmtree(release_dir)
        os.makedirs(release_dir)
        
        # 复制可执行文件
        exe_source = os.path.join("dist", "SerialDataVisualizer_Professional.exe")
        if os.path.exists(exe_source):
            shutil.copy2(exe_source, release_dir)
            print("✓ 可执行文件已复制")
        else:
            print("✗ 找不到可执行文件")
            return None
        
        # 复制配置文件
        config_files = [
            "config.ini",
            "使用说明.md",
            "README_Professional.md",
        ]
        
        for file in config_files:
            if os.path.exists(file):
                shutil.copy2(file, release_dir)
                print(f"✓ {file} 已复制")
        
        # 创建必要的目录
        dirs_to_create = ["data", "exports", "logs"]
        for dir_name in dirs_to_create:
            dir_path = os.path.join(release_dir, dir_name)
            os.makedirs(dir_path, exist_ok=True)
            print(f"✓ 目录 {dir_name} 已创建")
        
        # 复制文档目录
        if os.path.exists("docs"):
            shutil.copytree("docs", os.path.join(release_dir, "docs"))
            print("✓ 文档目录已复制")
        
        # 创建启动脚本
        create_batch_files(release_dir)
        
        # 创建README文件
        create_release_readme(release_dir)
        
        print(f"✓ 发布包创建完成: {release_dir}")
        return release_dir
        
    except Exception as e:
        print(f"✗ 创建发布包失败: {e}")
        return None

def create_batch_files(release_dir):
    """创建批处理文件"""
    
    # 启动程序.bat
    start_bat = '''@echo off
chcp 65001 > nul
title 串口数据可视化软件 - 专业版
echo.
echo ========================================
echo   串口数据可视化软件 - 专业版
echo   版权归属: 米醋电子工作室
echo ========================================
echo.
echo 正在启动专业版可视化软件...
echo.
SerialDataVisualizer_Professional.exe
if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查系统环境
    pause
)
'''
    
    with open(os.path.join(release_dir, "启动程序.bat"), 'w', encoding='gbk') as f:
        f.write(start_bat)
    
    # 演示模式.bat
    demo_bat = '''@echo off
chcp 65001 > nul
title 串口数据可视化软件 - 演示模式
echo.
echo ========================================
echo   串口数据可视化软件 - 演示模式
echo   版权归属: 米醋电子工作室
echo ========================================
echo.
echo 正在启动演示模式...
echo 注意: 演示模式使用模拟数据，无需连接串口设备
echo.
SerialDataVisualizer_Professional.exe
if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查系统环境
    pause
)
'''
    
    with open(os.path.join(release_dir, "演示模式.bat"), 'w', encoding='gbk') as f:
        f.write(demo_bat)
    
    print("✓ 批处理文件已创建")

def create_release_readme(release_dir):
    """创建发布版README"""
    readme_content = '''# 串口数据可视化软件 - 专业版

版权归属: 米醋电子工作室
版本: v1.1.0 Professional
发布日期: ''' + datetime.now().strftime("%Y-%m-%d") + '''

## 快速开始

### 方式一: 双击启动（推荐）
1. 双击 "启动程序.bat"
2. 或直接双击 "SerialDataVisualizer_Professional.exe"

### 方式二: 演示模式（无需串口设备）
1. 双击 "演示模式.bat"
2. 软件将使用模拟数据运行

## 软件特性

✅ 完全按照用户参考图设计
✅ 左侧3D坐标图显示前5个传感器
✅ 右侧传感器参数面板（按颜色分类）
✅ 实时时间显示
✅ 数据记录和导出功能
✅ 专业界面布局

## 使用说明

1. **连接设置**
   - 选择串口端口
   - 设置波特率（默认250000）
   - 点击"连接"按钮

2. **数据监控**
   - 左侧3D图：显示Normal 1-5传感器位置和数值
   - 右侧面板：显示所有10个传感器实时数值
   - 顶部：实时时间显示

3. **数据管理**
   - 点击"开始记录"保存数据
   - 点击"导出数据"保存为CSV文件

## 数据格式

输入数据格式: 1333,638,1331,1271,1327,629,1241,631,1259,629
- 前5个数值: Normal 1-5（3D可视化）
- 后5个数值: Spear 1-4 + Test（面板显示）

## 文件说明

- SerialDataVisualizer_Professional.exe - 主程序
- 启动程序.bat - 启动脚本
- 演示模式.bat - 演示模式启动脚本
- config.ini - 配置文件
- 使用说明.md - 详细使用说明
- README_Professional.md - 专业版说明
- data/ - 数据存储目录
- exports/ - 数据导出目录
- logs/ - 日志文件目录
- docs/ - 技术文档目录

## 技术支持

开发团队: 米醋电子工作室
技术支持: Alex (工程师)

如有问题，请查看详细的"使用说明.md"文件。
'''
    
    with open(os.path.join(release_dir, "README.txt"), 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 发布版README已创建")

def create_zip_package(release_dir):
    """创建ZIP压缩包"""
    if not release_dir:
        return None
    
    zip_filename = f"{release_dir}.zip"
    
    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(release_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_name = os.path.relpath(file_path, os.path.dirname(release_dir))
                    zipf.write(file_path, arc_name)
        
        print(f"✓ ZIP压缩包创建成功: {zip_filename}")
        return zip_filename
        
    except Exception as e:
        print(f"✗ 创建ZIP压缩包失败: {e}")
        return None

def main():
    """主函数"""
    print("=" * 60)
    print("专业版串口数据可视化软件打包工具")
    print("版权归属: 米醋电子工作室")
    print("=" * 60)
    
    # 检查PyInstaller
    if not check_pyinstaller():
        if not install_pyinstaller():
            print("无法安装PyInstaller，请手动安装后重试")
            return 1
    
    # 检查主程序文件
    if not os.path.exists("SerialDataVisualizer_Professional.py"):
        print("✗ 找不到主程序文件: SerialDataVisualizer_Professional.py")
        return 1
    
    print("✓ 主程序文件存在")
    
    # 创建spec文件
    create_spec_file()
    
    # 构建可执行文件
    if not build_executable():
        print("构建失败，请检查错误信息")
        return 1
    
    # 创建发布包
    release_dir = create_release_package()
    if not release_dir:
        print("创建发布包失败")
        return 1
    
    # 创建ZIP压缩包
    zip_file = create_zip_package(release_dir)
    
    print("\n" + "=" * 60)
    print("打包完成！")
    print("=" * 60)
    print(f"发布目录: {release_dir}")
    if zip_file:
        print(f"压缩包: {zip_file}")
    print("\n使用方法:")
    print(f"1. 进入 {release_dir} 目录")
    print("2. 双击 '启动程序.bat' 或直接运行 'SerialDataVisualizer_Professional.exe'")
    print("3. 或双击 '演示模式.bat' 运行演示模式")
    print("=" * 60)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
