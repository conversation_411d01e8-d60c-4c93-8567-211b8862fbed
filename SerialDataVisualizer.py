#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口数据可视化软件 - 独立版本
版权归属: 米醋电子工作室
创建日期: 2025-08-26
作者: <PERSON> (工程师)

这是专门用于打包的独立版本，包含完整功能但优化了依赖
"""

import sys
import os
import time
import threading
import signal
import argparse
from datetime import datetime
from typing import List, Optional, Dict, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 检查串口库
try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False

import numpy as np
import csv
import configparser
import logging
from logging.handlers import RotatingFileHandler


# ==================== 常量定义 ====================
APP_NAME = "串口数据可视化软件"
APP_VERSION = "1.0.0"
APP_AUTHOR = "米醋电子工作室"

SENSOR_POSITIONS = [
    (0.5, 0.5),  # 传感器1 - 中心
    (1.0, 1.0),  # 传感器2 - 右上
    (1.0, 0.0),  # 传感器3 - 右下
    (0.0, 1.0),  # 传感器4 - 左上
    (0.0, 0.0),  # 传感器5 - 左下
]

# 扩展波特率支持 - 包含高速波特率
SERIAL_BAUDRATES = [
    1200, 2400, 4800, 9600, 14400, 19200, 28800, 38400,
    57600, 76800, 115200, 128000, 153600, 230400, 250000,
    256000, 460800, 500000, 576000, 921600, 1000000, 1152000,
    1500000, 2000000, 2500000, 3000000
]

# 常用波特率 (用于快速选择)
COMMON_BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 250000, 460800, 921600]


# ==================== 日志系统 ====================
def setup_logger(name: str = "SerialVisualizer", level: int = logging.INFO) -> logging.Logger:
    """设置应用程序日志器"""
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    if logger.handlers:
        return logger
    
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    try:
        file_handler = RotatingFileHandler(
            "serial_visualizer.log", 
            maxBytes=10*1024*1024,
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    except:
        pass  # 如果无法创建文件日志，继续运行
    
    return logger


# ==================== 配置管理 ====================
class AppConfig:
    """应用程序配置管理器"""
    
    def __init__(self):
        self.config_file = "config.ini"
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file, encoding='utf-8')
            except:
                self.create_default_config()
        else:
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        self.config['SERIAL'] = {
            'port': 'COM1',
            'baudrate': '250000',  # 默认使用250000波特率
            'timeout': '1.0',
            'auto_reconnect': 'True'
        }
        
        self.config['DATA'] = {
            'buffer_size': '1000',
            'value_range_min': '0',
            'value_range_max': '4000'
        }
        
        self.save_config()
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
        except:
            pass
    
    def get(self, section: str, key: str, fallback: str = "") -> str:
        """获取配置值"""
        try:
            return self.config.get(section, key, fallback=fallback)
        except:
            return fallback
    
    def getint(self, section: str, key: str, fallback: int = 0) -> int:
        """获取整数配置值"""
        try:
            return self.config.getint(section, key, fallback=fallback)
        except:
            return fallback


# ==================== 数据结构 ====================
class SensorData:
    """传感器数据结构"""
    def __init__(self, timestamp, sensor_values, extended_data, raw_data):
        self.timestamp = timestamp
        self.sensor_values = sensor_values
        self.extended_data = extended_data
        self.raw_data = raw_data


# ==================== 串口管理器 ====================
class SerialManager:
    """串口通信管理器"""
    
    def __init__(self):
        self.logger = setup_logger()
        self.serial_port = None
        self.is_connected = False
        self.is_running = False
        
        # 连接参数
        self.port_name = "COM1"
        self.baudrate = 115200
        self.timeout = 1.0
        
        # 回调函数
        self.data_callback = None
        self.error_callback = None
        
        # 读取线程
        self.read_thread = None
    
    def scan_ports(self) -> List[str]:
        """扫描可用串口"""
        if not SERIAL_AVAILABLE:
            return ["COM1", "COM2", "COM3"]  # 模拟端口
        
        try:
            ports = serial.tools.list_ports.comports()
            return [port.device for port in ports]
        except Exception as e:
            self.logger.error(f"扫描串口失败: {e}")
            return []
    
    def set_connection_params(self, port: str, baudrate: int):
        """设置连接参数"""
        self.port_name = port
        self.baudrate = baudrate
    
    def set_callbacks(self, data_callback=None, error_callback=None):
        """设置回调函数"""
        self.data_callback = data_callback
        self.error_callback = error_callback
    
    def connect(self) -> bool:
        """连接串口"""
        if self.is_connected:
            return True
        
        if not SERIAL_AVAILABLE:
            self.logger.info(f"模拟连接到 {self.port_name}")
            self.is_connected = True
            self.is_running = True
            self.read_thread = threading.Thread(target=self._mock_data_loop, daemon=True)
            self.read_thread.start()
            return True
        
        try:
            self.serial_port = serial.Serial(
                port=self.port_name,
                baudrate=self.baudrate,
                bytesize=8,
                stopbits=1,
                parity='N',
                timeout=self.timeout
            )
            
            if self.serial_port.is_open:
                self.is_connected = True
                self.is_running = True
                self.read_thread = threading.Thread(target=self._read_data_loop, daemon=True)
                self.read_thread.start()
                self.logger.info(f"串口连接成功: {self.port_name}")
                return True
            else:
                raise Exception("串口打开失败")
                
        except Exception as e:
            self.logger.error(f"串口连接失败: {e}")
            if self.error_callback:
                self.error_callback(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        self.is_running = False
        
        if self.serial_port and self.serial_port.is_open:
            try:
                self.serial_port.close()
                self.logger.info("串口已断开")
            except Exception as e:
                self.logger.error(f"断开串口时出错: {e}")
        
        self.is_connected = False
        self.serial_port = None
    
    def _read_data_loop(self):
        """真实串口数据读取循环"""
        buffer = b''
        
        while self.is_running and self.serial_port and self.serial_port.is_open:
            try:
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    if data:
                        buffer += data
                        
                        while b'\n' in buffer:
                            line, buffer = buffer.split(b'\n', 1)
                            if line.strip() and self.data_callback:
                                self.data_callback(line.strip())
                
                time.sleep(0.01)
                
            except Exception as e:
                if self.is_running and self.error_callback:
                    self.error_callback(f"读取数据错误: {e}")
                break
    
    def _mock_data_loop(self):
        """模拟数据生成循环"""
        import random
        
        while self.is_running:
            try:
                # 生成模拟数据
                base_values = [1000, 1500, 2000, 2500, 3000, 100, 200, 300, 400, 500]
                mock_values = [base + random.randint(-200, 200) for base in base_values]
                mock_data = ','.join(map(str, mock_values))
                
                if self.data_callback:
                    self.data_callback(mock_data.encode())
                
                time.sleep(0.1)  # 10Hz数据率
                
            except Exception as e:
                if self.is_running and self.error_callback:
                    self.error_callback(f"模拟数据错误: {e}")
                break


# ==================== 数据解析器 ====================
class DataParser:
    """数据解析器"""
    
    def __init__(self):
        self.logger = setup_logger()
        self.total_packets = 0
        self.valid_packets = 0
        self.error_packets = 0
    
    def parse_data(self, raw_data: bytes) -> Optional[SensorData]:
        """解析原始数据"""
        self.total_packets += 1
        
        try:
            data_str = raw_data.decode('utf-8', errors='ignore').strip()
            if not data_str:
                return None
            
            # 尝试不同的分隔符
            values = None
            for delimiter in [',', ' ', ';']:
                if delimiter in data_str:
                    parts = data_str.split(delimiter)
                    break
            else:
                parts = data_str.split()
            
            # 过滤空字符串并转换为浮点数
            parts = [part.strip() for part in parts if part.strip()]
            
            if len(parts) >= 10:
                values = [float(part) for part in parts[:10]]
            elif len(parts) >= 5:
                values = [float(part) for part in parts[:5]]
                values.extend([0.0] * (10 - len(values)))  # 补齐到10个
            else:
                raise ValueError(f"数据包大小不足: {len(parts)}")
            
            # 分离传感器数据和扩展数据
            sensor_values = values[:5]
            extended_data = values[5:10]
            
            sensor_data = SensorData(
                timestamp=datetime.now(),
                sensor_values=sensor_values,
                extended_data=extended_data,
                raw_data=data_str
            )
            
            self.valid_packets += 1
            return sensor_data
            
        except Exception as e:
            self.error_packets += 1
            self.logger.error(f"数据解析失败: {e}")
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取解析统计信息"""
        if self.total_packets > 0:
            success_rate = (self.valid_packets / self.total_packets) * 100
        else:
            success_rate = 0
        
        return {
            'total_packets': self.total_packets,
            'valid_packets': self.valid_packets,
            'error_packets': self.error_packets,
            'success_rate': round(success_rate, 2)
        }


# ==================== 文件处理器 ====================
class FileHandler:
    """文件处理器"""
    
    def __init__(self):
        self.logger = setup_logger()
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        directories = ['exports', 'data']
        for directory in directories:
            if not os.path.exists(directory):
                try:
                    os.makedirs(directory)
                except:
                    pass
    
    def save_to_csv(self, data: List[Dict[str, Any]], filename: str = None) -> str:
        """保存数据到CSV文件"""
        if not data:
            raise ValueError("没有数据可保存")
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"sensor_data_{timestamp}.csv"
        
        filepath = os.path.join('exports', filename)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            
            self.logger.info(f"数据已保存到CSV文件: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存CSV文件失败: {e}")
            raise


# ==================== 主应用程序 ====================
class SerialDataVisualizerApp:
    """串口数据可视化应用程序"""
    
    def __init__(self):
        self.logger = setup_logger()
        self.config = AppConfig()
        self.file_handler = FileHandler()
        
        # 组件
        self.serial_manager = SerialManager()
        self.data_parser = DataParser()
        
        # 数据
        self.current_data = None
        self.data_history = []
        self.is_recording = False
        self.start_time = None
        
        # 状态
        self.is_running = False
        
        # 设置回调
        self.serial_manager.set_callbacks(
            data_callback=self._on_data_received,
            error_callback=self._on_error
        )
        
        # 信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n正在关闭程序...")
        self.stop()
        sys.exit(0)
    
    def _on_data_received(self, raw_data: bytes):
        """数据接收回调"""
        sensor_data = self.data_parser.parse_data(raw_data)
        if sensor_data:
            self.current_data = sensor_data
            if self.is_recording:
                self.data_history.append(sensor_data)
    
    def _on_error(self, error_msg: str):
        """错误回调"""
        self.logger.error(error_msg)
    
    def start_interactive_mode(self):
        """启动交互模式"""
        print("=" * 60)
        print(f"           {APP_NAME} v{APP_VERSION}")
        print(f"              版权归属: {APP_AUTHOR}")
        print("=" * 60)
        print()
        
        # 扫描端口
        available_ports = self.serial_manager.scan_ports()
        print(f"可用端口: {', '.join(available_ports) if available_ports else '无'}")
        
        # 选择端口
        if available_ports:
            print("\n请选择串口端口:")
            for i, port in enumerate(available_ports):
                print(f"{i+1}. {port}")
            
            try:
                choice = input(f"请输入选择 (1-{len(available_ports)}) 或直接回车使用 {available_ports[0]}: ").strip()
                if choice and choice.isdigit():
                    port_index = int(choice) - 1
                    if 0 <= port_index < len(available_ports):
                        selected_port = available_ports[port_index]
                    else:
                        selected_port = available_ports[0]
                else:
                    selected_port = available_ports[0]
            except:
                selected_port = available_ports[0]
        else:
            selected_port = "COM1"
        
        # 选择波特率 - 优化显示
        print(f"\n常用波特率:")
        for i, rate in enumerate(COMMON_BAUDRATES):
            print(f"{i+1:2d}. {rate:>8d}")

        print(f"\n完整波特率列表: {', '.join(map(str, SERIAL_BAUDRATES))}")

        try:
            baudrate_input = input("请输入波特率数字或选择序号 (默认: 115200): ").strip()

            if baudrate_input and baudrate_input.isdigit():
                input_num = int(baudrate_input)

                # 检查是否是选择序号
                if 1 <= input_num <= len(COMMON_BAUDRATES):
                    selected_baudrate = COMMON_BAUDRATES[input_num - 1]
                    print(f"已选择: {selected_baudrate}")
                # 检查是否是直接输入的波特率
                elif input_num in SERIAL_BAUDRATES:
                    selected_baudrate = input_num
                else:
                    print(f"警告: {input_num} 不在支持的波特率列表中，但将尝试使用")
                    selected_baudrate = input_num
            else:
                selected_baudrate = 115200
        except:
            selected_baudrate = 115200
        
        # 连接串口
        print(f"\n正在连接到 {selected_port}, 波特率: {selected_baudrate}...")
        self.serial_manager.set_connection_params(selected_port, selected_baudrate)
        
        if self.serial_manager.connect():
            print("✓ 连接成功!")
            if not SERIAL_AVAILABLE:
                print("注意: 使用模拟数据模式")
        else:
            print("✗ 连接失败")
            return
        
        # 启动主循环
        self.is_running = True
        self.start_time = datetime.now()
        
        print("\n程序已启动，按 Ctrl+C 退出")
        print("命令: r=开始/停止记录, e=导出数据, s=显示统计, q=退出")
        print("-" * 60)
        
        try:
            self._main_loop()
        except KeyboardInterrupt:
            pass
        
        self.stop()
    
    def _main_loop(self):
        """主循环"""
        last_display_time = 0
        
        while self.is_running:
            current_time = time.time()
            
            # 每秒更新一次显示
            if current_time - last_display_time >= 1.0:
                self._update_display()
                last_display_time = current_time
            
            # 检查用户输入
            self._check_user_input()
            
            time.sleep(0.1)
    
    def _update_display(self):
        """更新显示"""
        # 清屏
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("=" * 60)
        print(f"           {APP_NAME} - 实时监控")
        print("=" * 60)
        
        # 连接状态
        status = "已连接" if self.serial_manager.is_connected else "未连接"
        print(f"连接状态: {status}")
        
        if self.is_recording:
            print("🔴 正在记录数据")
        
        print()
        
        # 当前数据
        if self.current_data:
            timestamp_str = self.current_data.timestamp.strftime("%H:%M:%S.%f")[:-3]
            print(f"时间戳: {timestamp_str}")
            print()
            
            print("传感器数据:")
            for i, (value, pos) in enumerate(zip(self.current_data.sensor_values, SENSOR_POSITIONS)):
                print(f"  传感器{i+1} [{pos[0]:.1f}, {pos[1]:.1f}]: {value:8.2f}")
            
            print()
            
            # 简单的ASCII可视化
            print("可视化 (相对大小):")
            max_val = max(self.current_data.sensor_values) if self.current_data.sensor_values else 1
            for i, value in enumerate(self.current_data.sensor_values):
                bar_length = int((value / max_val) * 30) if max_val > 0 else 0
                bar = "█" * bar_length + "░" * (30 - bar_length)
                print(f"  传感器{i+1}: {bar} {value:6.1f}")
        else:
            print("等待数据...")
        
        print()
        
        # 统计信息
        stats = self.data_parser.get_statistics()
        elapsed = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        
        print("统计信息:")
        print(f"  运行时间: {elapsed:.1f} 秒")
        print(f"  数据包总数: {stats['total_packets']}")
        print(f"  有效数据: {stats['valid_packets']}")
        print(f"  成功率: {stats['success_rate']:.1f}%")
        
        if self.is_recording:
            print(f"  已记录: {len(self.data_history)} 条")
        
        print()
        print("命令: r=记录切换, e=导出数据, s=统计详情, q=退出")
    
    def _check_user_input(self):
        """检查用户输入 (非阻塞)"""
        # 简化版本，使用文件方式接收命令
        try:
            if os.path.exists('command.txt'):
                with open('command.txt', 'r') as f:
                    command = f.read().strip().lower()
                
                os.remove('command.txt')
                
                if command == 'r':
                    self._toggle_recording()
                elif command == 'e':
                    self._export_data()
                elif command == 's':
                    self._show_statistics()
                elif command == 'q':
                    self.is_running = False
        except:
            pass
    
    def _toggle_recording(self):
        """切换记录状态"""
        self.is_recording = not self.is_recording
        if self.is_recording:
            self.data_history.clear()
            self.logger.info("开始数据记录")
        else:
            self.logger.info(f"停止数据记录，共记录 {len(self.data_history)} 条数据")
    
    def _export_data(self):
        """导出数据"""
        if not self.data_history:
            self.logger.warning("没有数据可导出")
            return
        
        try:
            # 转换数据格式
            export_data = []
            for sensor_data in self.data_history:
                row = {
                    'timestamp': sensor_data.timestamp.isoformat(),
                    'sensor1': sensor_data.sensor_values[0],
                    'sensor2': sensor_data.sensor_values[1],
                    'sensor3': sensor_data.sensor_values[2],
                    'sensor4': sensor_data.sensor_values[3],
                    'sensor5': sensor_data.sensor_values[4],
                    'ext1': sensor_data.extended_data[0],
                    'ext2': sensor_data.extended_data[1],
                    'ext3': sensor_data.extended_data[2],
                    'ext4': sensor_data.extended_data[3],
                    'ext5': sensor_data.extended_data[4],
                    'raw_data': sensor_data.raw_data
                }
                export_data.append(row)
            
            # 保存到文件
            filepath = self.file_handler.save_to_csv(export_data)
            self.logger.info(f"数据已导出到: {filepath}")
            
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
    
    def _show_statistics(self):
        """显示详细统计"""
        stats = self.data_parser.get_statistics()
        elapsed = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0
        
        print("\n详细统计信息:")
        print(f"运行时间: {elapsed:.1f} 秒")
        print(f"数据包总数: {stats['total_packets']}")
        print(f"有效数据包: {stats['valid_packets']}")
        print(f"错误数据包: {stats['error_packets']}")
        print(f"成功率: {stats['success_rate']:.1f}%")
        
        if elapsed > 0:
            print(f"数据接收率: {stats['total_packets']/elapsed:.1f} 包/秒")
        
        if self.data_history:
            print(f"已记录数据: {len(self.data_history)} 条")
        
        input("\n按回车键继续...")
    
    def stop(self):
        """停止程序"""
        self.is_running = False
        self.serial_manager.disconnect()
        
        # 自动导出数据
        if self.data_history:
            print(f"\n自动导出 {len(self.data_history)} 条记录的数据...")
            try:
                self._export_data()
            except:
                pass
        
        print("程序已退出")


# ==================== 主函数 ====================
def main():
    """主函数"""
    parser = argparse.ArgumentParser(description=f'{APP_NAME} v{APP_VERSION}')
    parser.add_argument('--port', '-p', type=str, help='串口端口 (如: COM1)')
    parser.add_argument('--baudrate', '-b', type=int, default=250000, help='波特率 (默认: 250000, 支持1200-3000000)')
    parser.add_argument('--demo', action='store_true', help='运行演示模式')
    
    args = parser.parse_args()
    
    app = SerialDataVisualizerApp()
    
    if args.demo:
        # 演示模式
        print("启动演示模式...")
        app.serial_manager.set_connection_params("DEMO", 115200)
        app.serial_manager.connect()
        app.is_recording = True
        app.start_time = datetime.now()
        
        try:
            for i in range(20):  # 运行20秒
                time.sleep(1)
                app._update_display()
        except KeyboardInterrupt:
            pass
        
        app.stop()
    else:
        # 交互模式
        if args.port:
            app.serial_manager.set_connection_params(args.port, args.baudrate)
            if app.serial_manager.connect():
                app.start_interactive_mode()
            else:
                print("连接失败")
                return 1
        else:
            app.start_interactive_mode()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
