#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口数据可视化软件 - 主程序入口
版权归属: 米醋电子工作室
创建日期: 2025-08-26
作者: <PERSON> (工程师)
"""

import sys
import os
import logging
from datetime import datetime
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt, QDir
from PyQt5.QtGui import QIcon

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.main_window import MainWindow
from utils.logger import setup_logger
from config.settings import AppConfig


class SerialVisualizerApp(QApplication):
    """串口数据可视化应用程序主类"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # 设置应用程序属性
        self.setApplicationName("串口数据可视化软件")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("米醋电子工作室")
        
        # 设置高DPI支持
        self.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        self.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        # 初始化配置和日志
        self.config = AppConfig()
        self.logger = setup_logger()
        
        # 创建主窗口
        self.main_window = None
        
        # 设置异常处理
        sys.excepthook = self.handle_exception
        
    def initialize(self):
        """初始化应用程序"""
        try:
            self.logger.info("应用程序启动中...")
            
            # 创建主窗口
            self.main_window = MainWindow()
            self.main_window.show()
            
            self.logger.info("应用程序启动成功")
            return True
            
        except Exception as e:
            self.logger.error(f"应用程序初始化失败: {e}")
            QMessageBox.critical(None, "启动错误", f"应用程序启动失败:\n{str(e)}")
            return False
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        error_msg = f"未处理的异常: {exc_type.__name__}: {exc_value}"
        self.logger.error(error_msg, exc_info=(exc_type, exc_value, exc_traceback))
        
        # 显示错误对话框
        if self.main_window:
            QMessageBox.critical(
                self.main_window, 
                "程序错误", 
                f"程序遇到未处理的错误:\n{error_msg}\n\n请查看日志文件获取详细信息。"
            )


def main():
    """主函数"""
    # 创建应用程序实例
    app = SerialVisualizerApp(sys.argv)
    
    # 初始化应用程序
    if not app.initialize():
        return 1
    
    # 运行应用程序
    try:
        return app.exec_()
    except Exception as e:
        print(f"应用程序运行错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
