#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenGL 3D可视化组件
版权归属: 米醋电子工作室
"""

import math
import numpy as np
from typing import List, Tuple, Optional
from PyQt5.QtOpenGL import Q<PERSON><PERSON>GLWidget
from PyQt5.QtCore import QTimer, pyqtSignal
from PyQt5.QtGui import QMouseEvent, QWheelEvent
from OpenGL.GL import *
from OpenGL.GLU import *
from core.data_parser import SensorData
from config.constants import RENDER_SETTINGS, COLORS, SENSOR_POSITIONS, DATA_RANGES
from utils.logger import get_logger


class OpenGL3DWidget(QOpenGLWidget):
    """3D可视化核心组件"""
    
    # 信号定义
    mouse_position_changed = pyqtSignal(float, float)  # 鼠标位置变化
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger()
        
        # 当前传感器数据
        self.current_data: Optional[SensorData] = None
        
        # 相机参数
        self.camera_distance = RENDER_SETTINGS['CAMERA_DISTANCE']
        self.camera_rotation_x = 30.0
        self.camera_rotation_y = 45.0
        self.camera_target = [0.5, 0.5, 0.0]
        
        # 鼠标交互
        self.last_mouse_pos = None
        self.mouse_pressed = False
        
        # 渲染设置
        self.show_grid = True
        self.show_axes = True
        self.show_labels = True
        self.animation_enabled = True
        
        # 动画参数
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update)
        self.animation_timer.start(33)  # 约30fps
        
        # 传感器球体显示列表
        self.sphere_display_list = None
        
        self.logger.info("OpenGL 3D组件初始化完成")
    
    def initializeGL(self):
        """初始化OpenGL环境"""
        try:
            # 设置背景色
            bg_color = COLORS['BACKGROUND']
            glClearColor(bg_color[0], bg_color[1], bg_color[2], bg_color[3])
            
            # 启用深度测试
            glEnable(GL_DEPTH_TEST)
            glDepthFunc(GL_LESS)
            
            # 启用混合
            glEnable(GL_BLEND)
            glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
            
            # 启用光照
            glEnable(GL_LIGHTING)
            glEnable(GL_LIGHT0)
            
            # 设置光源
            light_pos = [2.0, 2.0, 2.0, 1.0]
            light_ambient = [0.3, 0.3, 0.3, 1.0]
            light_diffuse = [0.8, 0.8, 0.8, 1.0]
            light_specular = [1.0, 1.0, 1.0, 1.0]
            
            glLightfv(GL_LIGHT0, GL_POSITION, light_pos)
            glLightfv(GL_LIGHT0, GL_AMBIENT, light_ambient)
            glLightfv(GL_LIGHT0, GL_DIFFUSE, light_diffuse)
            glLightfv(GL_LIGHT0, GL_SPECULAR, light_specular)
            
            # 创建球体显示列表
            self._create_sphere_display_list()
            
            self.logger.info("OpenGL环境初始化完成")
            
        except Exception as e:
            self.logger.error(f"OpenGL初始化失败: {e}")
    
    def resizeGL(self, width: int, height: int):
        """调整视口大小"""
        if height == 0:
            height = 1
        
        glViewport(0, 0, width, height)
        
        # 设置投影矩阵
        glMatrixMode(GL_PROJECTION)
        glLoadIdentity()
        
        aspect_ratio = width / height
        gluPerspective(
            RENDER_SETTINGS['FOV'], 
            aspect_ratio, 
            RENDER_SETTINGS['NEAR_PLANE'], 
            RENDER_SETTINGS['FAR_PLANE']
        )
        
        glMatrixMode(GL_MODELVIEW)
    
    def paintGL(self):
        """渲染场景"""
        try:
            # 清除缓冲区
            glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT)
            
            # 设置模型视图矩阵
            glLoadIdentity()
            
            # 设置相机位置
            self._setup_camera()
            
            # 绘制坐标系
            if self.show_axes:
                self._draw_axes()
            
            # 绘制网格
            if self.show_grid:
                self._draw_grid()
            
            # 绘制传感器数据
            if self.current_data:
                self._draw_sensors()
            
            # 绘制标签
            if self.show_labels:
                self._draw_labels()
                
        except Exception as e:
            self.logger.error(f"渲染错误: {e}")
    
    def _setup_camera(self):
        """设置相机位置和方向"""
        # 计算相机位置
        rad_x = math.radians(self.camera_rotation_x)
        rad_y = math.radians(self.camera_rotation_y)
        
        camera_x = self.camera_target[0] + self.camera_distance * math.cos(rad_x) * math.cos(rad_y)
        camera_y = self.camera_target[1] + self.camera_distance * math.cos(rad_x) * math.sin(rad_y)
        camera_z = self.camera_target[2] + self.camera_distance * math.sin(rad_x)
        
        gluLookAt(
            camera_x, camera_y, camera_z,  # 相机位置
            self.camera_target[0], self.camera_target[1], self.camera_target[2],  # 目标位置
            0.0, 0.0, 1.0  # 上方向
        )
    
    def _draw_axes(self):
        """绘制坐标轴"""
        glDisable(GL_LIGHTING)
        glLineWidth(2.0)
        
        axis_length = RENDER_SETTINGS['AXIS_LENGTH']
        
        glBegin(GL_LINES)
        
        # X轴 - 红色
        glColor3f(1.0, 0.0, 0.0)
        glVertex3f(0.0, 0.0, 0.0)
        glVertex3f(axis_length, 0.0, 0.0)
        
        # Y轴 - 绿色
        glColor3f(0.0, 1.0, 0.0)
        glVertex3f(0.0, 0.0, 0.0)
        glVertex3f(0.0, axis_length, 0.0)
        
        # Z轴 - 蓝色
        glColor3f(0.0, 0.0, 1.0)
        glVertex3f(0.0, 0.0, 0.0)
        glVertex3f(0.0, 0.0, axis_length)
        
        glEnd()
        
        glEnable(GL_LIGHTING)
    
    def _draw_grid(self):
        """绘制网格"""
        glDisable(GL_LIGHTING)
        glLineWidth(1.0)
        
        grid_color = COLORS['GRID']
        glColor4f(grid_color[0], grid_color[1], grid_color[2], grid_color[3])
        
        grid_size = RENDER_SETTINGS['GRID_SIZE']
        step = 1.0 / grid_size
        
        glBegin(GL_LINES)
        
        # 绘制XY平面网格
        for i in range(grid_size + 1):
            x = i * step
            y = i * step
            
            # 垂直线
            glVertex3f(x, 0.0, 0.0)
            glVertex3f(x, 1.0, 0.0)
            
            # 水平线
            glVertex3f(0.0, y, 0.0)
            glVertex3f(1.0, y, 0.0)
        
        glEnd()
        
        glEnable(GL_LIGHTING)
    
    def _draw_sensors(self):
        """绘制传感器数据点"""
        if not self.current_data or not self.sphere_display_list:
            return
        
        glEnable(GL_LIGHTING)
        
        for i, (value, position) in enumerate(zip(self.current_data.sensor_values, SENSOR_POSITIONS)):
            # 计算Z轴高度
            z_height = value * DATA_RANGES['Z_SCALE_FACTOR']
            
            # 根据数值选择颜色
            color = self._get_sensor_color(value)
            
            # 设置材质属性
            glMaterialfv(GL_FRONT, GL_AMBIENT_AND_DIFFUSE, color)
            glMaterialfv(GL_FRONT, GL_SPECULAR, [1.0, 1.0, 1.0, 1.0])
            glMaterialf(GL_FRONT, GL_SHININESS, 50.0)
            
            # 绘制球体
            glPushMatrix()
            glTranslatef(position[0], position[1], z_height)
            glScalef(RENDER_SETTINGS['SPHERE_RADIUS'], 
                    RENDER_SETTINGS['SPHERE_RADIUS'], 
                    RENDER_SETTINGS['SPHERE_RADIUS'])
            glCallList(self.sphere_display_list)
            glPopMatrix()
    
    def _get_sensor_color(self, value: float) -> List[float]:
        """根据传感器数值获取颜色"""
        # 归一化数值到0-1范围
        normalized = (value - DATA_RANGES['SENSOR_MIN']) / (DATA_RANGES['SENSOR_MAX'] - DATA_RANGES['SENSOR_MIN'])
        normalized = max(0.0, min(1.0, normalized))  # 限制在0-1范围内
        
        # 使用热力图颜色映射 (蓝色->绿色->黄色->红色)
        if normalized < 0.25:
            # 蓝色到青色
            r = 0.0
            g = normalized * 4.0
            b = 1.0
        elif normalized < 0.5:
            # 青色到绿色
            r = 0.0
            g = 1.0
            b = 1.0 - (normalized - 0.25) * 4.0
        elif normalized < 0.75:
            # 绿色到黄色
            r = (normalized - 0.5) * 4.0
            g = 1.0
            b = 0.0
        else:
            # 黄色到红色
            r = 1.0
            g = 1.0 - (normalized - 0.75) * 4.0
            b = 0.0
        
        return [r, g, b, 0.8]
    
    def _create_sphere_display_list(self):
        """创建球体显示列表"""
        self.sphere_display_list = glGenLists(1)
        glNewList(self.sphere_display_list, GL_COMPILE)
        
        # 使用GLU库创建球体
        quadric = gluNewQuadric()
        gluQuadricNormals(quadric, GLU_SMOOTH)
        gluSphere(quadric, 1.0, RENDER_SETTINGS['SPHERE_SEGMENTS'], RENDER_SETTINGS['SPHERE_SEGMENTS'])
        gluDeleteQuadric(quadric)
        
        glEndList()
    
    def _draw_labels(self):
        """绘制标签 (简化版本)"""
        # 注意: 在OpenGL中绘制文本比较复杂，这里只绘制简单的标记
        glDisable(GL_LIGHTING)
        glPointSize(3.0)
        glColor3f(1.0, 1.0, 1.0)
        
        glBegin(GL_POINTS)
        for i, position in enumerate(SENSOR_POSITIONS):
            glVertex3f(position[0], position[1], -0.05)
        glEnd()
        
        glEnable(GL_LIGHTING)
    
    def update_sensor_data(self, sensor_data: SensorData):
        """更新传感器数据"""
        self.current_data = sensor_data
        self.update()  # 触发重绘
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标按下事件"""
        self.last_mouse_pos = event.pos()
        self.mouse_pressed = True
    
    def mouseReleaseEvent(self, event: QMouseEvent):
        """鼠标释放事件"""
        self.mouse_pressed = False
    
    def mouseMoveEvent(self, event: QMouseEvent):
        """鼠标移动事件"""
        if self.mouse_pressed and self.last_mouse_pos:
            dx = event.x() - self.last_mouse_pos.x()
            dy = event.y() - self.last_mouse_pos.y()
            
            # 旋转相机
            self.camera_rotation_y += dx * 0.5
            self.camera_rotation_x += dy * 0.5
            
            # 限制X轴旋转角度
            self.camera_rotation_x = max(-90, min(90, self.camera_rotation_x))
            
            self.last_mouse_pos = event.pos()
            self.update()
    
    def wheelEvent(self, event: QWheelEvent):
        """鼠标滚轮事件"""
        delta = event.angleDelta().y()
        zoom_factor = 1.1 if delta > 0 else 0.9
        
        self.camera_distance *= zoom_factor
        self.camera_distance = max(1.0, min(10.0, self.camera_distance))
        
        self.update()
    
    def reset_view(self):
        """重置视图"""
        self.camera_distance = RENDER_SETTINGS['CAMERA_DISTANCE']
        self.camera_rotation_x = 30.0
        self.camera_rotation_y = 45.0
        self.camera_target = [0.5, 0.5, 0.0]
        self.update()
    
    def set_render_options(self, show_grid: bool = True, show_axes: bool = True, show_labels: bool = True):
        """设置渲染选项"""
        self.show_grid = show_grid
        self.show_axes = show_axes
        self.show_labels = show_labels
        self.update()

    def get_current_data_values(self) -> List[float]:
        """获取当前数据值"""
        if self.current_data:
            return self.current_data.sensor_values
        return [0.0] * 5
