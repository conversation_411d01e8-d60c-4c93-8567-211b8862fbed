#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
版权归属: 米醋电子工作室
"""

import os
import configparser
from typing import Dict, Any


class AppConfig:
    """应用程序配置管理器"""
    
    def __init__(self):
        self.config_file = "config.ini"
        self.config = configparser.ConfigParser()
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file, encoding='utf-8')
        else:
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        # 串口配置
        self.config['SERIAL'] = {
            'port': 'COM1',
            'baudrate': '115200',
            'databits': '8',
            'stopbits': '1',
            'parity': 'N',
            'timeout': '1.0',
            'auto_reconnect': 'True'
        }
        
        # 显示配置
        self.config['DISPLAY'] = {
            'window_width': '1200',
            'window_height': '800',
            'fps_limit': '60',
            'show_grid': 'True',
            'show_axes': 'True',
            'background_color': '0.2,0.2,0.2,1.0'
        }
        
        # 数据配置
        self.config['DATA'] = {
            'buffer_size': '1000',
            'auto_save': 'False',
            'save_interval': '60',
            'data_format': 'csv',
            'log_level': 'INFO'
        }
        
        # 传感器配置
        self.config['SENSORS'] = {
            'sensor1_pos': '0.5,0.5',
            'sensor2_pos': '1.0,1.0', 
            'sensor3_pos': '1.0,0.0',
            'sensor4_pos': '0.0,1.0',
            'sensor5_pos': '0.0,0.0',
            'value_range_min': '0',
            'value_range_max': '4000',
            'color_map': 'viridis'
        }
        
        self.save_config()
    
    def save_config(self):
        """保存配置到文件"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            self.config.write(f)
    
    def get(self, section: str, key: str, fallback: Any = None) -> str:
        """获取配置值"""
        return self.config.get(section, key, fallback=fallback)
    
    def getint(self, section: str, key: str, fallback: int = 0) -> int:
        """获取整数配置值"""
        return self.config.getint(section, key, fallback=fallback)
    
    def getfloat(self, section: str, key: str, fallback: float = 0.0) -> float:
        """获取浮点数配置值"""
        return self.config.getfloat(section, key, fallback=fallback)
    
    def getboolean(self, section: str, key: str, fallback: bool = False) -> bool:
        """获取布尔配置值"""
        return self.config.getboolean(section, key, fallback=fallback)
    
    def set(self, section: str, key: str, value: str):
        """设置配置值"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, str(value))
        self.save_config()
    
    def get_serial_config(self) -> Dict[str, Any]:
        """获取串口配置"""
        return {
            'port': self.get('SERIAL', 'port', 'COM1'),
            'baudrate': self.getint('SERIAL', 'baudrate', 115200),
            'databits': self.getint('SERIAL', 'databits', 8),
            'stopbits': self.getint('SERIAL', 'stopbits', 1),
            'parity': self.get('SERIAL', 'parity', 'N'),
            'timeout': self.getfloat('SERIAL', 'timeout', 1.0),
            'auto_reconnect': self.getboolean('SERIAL', 'auto_reconnect', True)
        }
    
    def get_sensor_positions(self) -> list:
        """获取传感器位置配置"""
        positions = []
        for i in range(1, 6):
            pos_str = self.get('SENSORS', f'sensor{i}_pos', '0,0')
            x, y = map(float, pos_str.split(','))
            positions.append((x, y))
        return positions
    
    def get_display_config(self) -> Dict[str, Any]:
        """获取显示配置"""
        return {
            'window_width': self.getint('DISPLAY', 'window_width', 1200),
            'window_height': self.getint('DISPLAY', 'window_height', 800),
            'fps_limit': self.getint('DISPLAY', 'fps_limit', 60),
            'show_grid': self.getboolean('DISPLAY', 'show_grid', True),
            'show_axes': self.getboolean('DISPLAY', 'show_axes', True)
        }
