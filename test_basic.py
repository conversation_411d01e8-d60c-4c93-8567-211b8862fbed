#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础功能测试脚本
版权归属: 米醋电子工作室
"""

import sys
import os
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from config.settings import AppConfig
        from config.constants import APP_NAME, APP_VERSION
        from utils.logger import setup_logger
        from core.data_parser import DataParser, SensorData
        print("✓ 核心模块导入成功")
        
        # 测试配置
        config = AppConfig()
        print(f"✓ 配置管理器初始化成功")
        
        # 测试日志
        logger = setup_logger()
        logger.info("测试日志消息")
        print("✓ 日志系统初始化成功")
        
        # 测试数据解析器
        parser = DataParser()
        print("✓ 数据解析器初始化成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_data_parsing():
    """测试数据解析功能"""
    print("\n测试数据解析功能...")
    
    try:
        from core.data_parser import DataParser
        
        parser = DataParser()
        
        # 测试数据
        test_data_samples = [
            b"1000,1500,2000,2500,3000,100,200,300,400,500",
            b"1100 1600 2100 2600 3100 110 210 310 410 510",
            b"1200;1700;2200;2700;3200;120;220;320;420;520",
        ]
        
        success_count = 0
        for i, test_data in enumerate(test_data_samples):
            result = parser.parse_data(test_data)
            if result:
                print(f"  ✓ 测试数据 {i+1}: 解析成功")
                print(f"    传感器数值: {result.sensor_values}")
                success_count += 1
            else:
                print(f"  ✗ 测试数据 {i+1}: 解析失败")
        
        print(f"数据解析测试完成: {success_count}/{len(test_data_samples)} 成功")
        return success_count == len(test_data_samples)
        
    except Exception as e:
        print(f"✗ 数据解析测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    print("\n测试文件操作...")
    
    try:
        from utils.file_handler import DataFileHandler
        
        file_handler = DataFileHandler()
        
        # 测试数据
        test_data = [
            {
                'timestamp': datetime.now().isoformat(),
                'sensor1': 1000.0,
                'sensor2': 1500.0,
                'sensor3': 2000.0,
                'sensor4': 2500.0,
                'sensor5': 3000.0
            }
        ]
        
        # 测试保存
        filename = "test_export.csv"
        filepath = file_handler.save_to_csv(test_data, filename)
        print(f"✓ CSV文件保存成功: {filepath}")
        
        # 测试加载
        loaded_data = file_handler.load_from_csv(filepath)
        print(f"✓ CSV文件加载成功: {len(loaded_data)} 条记录")
        
        # 清理测试文件
        if os.path.exists(filepath):
            os.remove(filepath)
            print("✓ 测试文件已清理")
        
        return True
        
    except Exception as e:
        print(f"✗ 文件操作测试失败: {e}")
        return False

def test_serial_manager():
    """测试串口管理器 (不实际连接)"""
    print("\n测试串口管理器...")
    
    try:
        from core.serial_manager import SerialManager
        
        serial_mgr = SerialManager()
        
        # 测试端口扫描
        ports = serial_mgr.scan_ports()
        print(f"✓ 端口扫描成功: 发现 {len(ports)} 个端口")
        if ports:
            print(f"  可用端口: {', '.join(ports)}")
        
        # 测试参数设置
        serial_mgr.set_connection_params("COM1", 115200)
        print("✓ 串口参数设置成功")
        
        # 测试状态获取
        status = serial_mgr.get_status()
        print(f"✓ 状态获取成功: {status}")
        
        return True
        
    except Exception as e:
        print(f"✗ 串口管理器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print(f"开始基础功能测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("数据解析", test_data_parsing),
        ("文件操作", test_file_operations),
        ("串口管理", test_serial_manager),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 测试通过")
            else:
                print(f"✗ {test_name} 测试失败")
        except Exception as e:
            print(f"✗ {test_name} 测试异常: {e}")
        
        print("-" * 30)
    
    print(f"\n测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有基础功能测试通过!")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
