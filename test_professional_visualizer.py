#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业可视化软件测试脚本
版权归属: 米醋电子工作室
创建日期: 2025-08-26
作者: <PERSON> (工程师)

使用playwright进行自动化测试
"""

import sys
import os
import time
import threading
import subprocess
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_professional_visualizer():
    """测试专业可视化软件"""
    print("=" * 60)
    print("专业可视化软件测试")
    print("=" * 60)
    
    try:
        # 导入专业可视化模块
        from SerialDataVisualizer_Professional import (
            ProfessionalVisualizerGUI, 
            SerialManager, 
            DataParser,
            SensorData,
            SENSOR_POSITIONS,
            SENSOR_LABELS
        )
        
        print("✓ 模块导入成功")
        
        # 测试数据结构
        print("\n1. 测试数据结构...")
        test_data = SensorData(
            timestamp=datetime.now(),
            sensor_values=[1333.0, 638.0, 1331.0, 1271.0, 1327.0],
            extended_data=[629.0, 1241.0, 631.0, 1259.0, 629.0],
            raw_data="1333,638,1331,1271,1327,629,1241,631,1259,629"
        )
        print(f"✓ 传感器数据创建成功: {len(test_data.sensor_values)} + {len(test_data.extended_data)} 个数据点")
        
        # 测试串口管理器
        print("\n2. 测试串口管理器...")
        serial_mgr = SerialManager()
        ports = serial_mgr.scan_ports()
        print(f"✓ 扫描到端口: {ports}")
        
        # 测试数据解析器
        print("\n3. 测试数据解析器...")
        parser = DataParser()
        test_raw_data = b"1333,638,1331,1271,1327,629,1241,631,1259,629"
        parsed_data = parser.parse_data(test_raw_data)
        if parsed_data:
            print(f"✓ 数据解析成功: Normal={parsed_data.sensor_values}, Extended={parsed_data.extended_data}")
        else:
            print("✗ 数据解析失败")
        
        # 测试传感器位置和标签
        print("\n4. 测试传感器配置...")
        print(f"✓ 传感器位置: {SENSOR_POSITIONS}")
        print(f"✓ 传感器标签: {SENSOR_LABELS}")
        
        # 验证与参考图的一致性
        print("\n5. 验证与参考图的一致性...")
        expected_positions = [(0.5, 0.5), (1.0, 1.0), (1.0, 0.0), (0.0, 1.0), (0.0, 0.0)]
        expected_labels = [
            "Normal 1", "Normal 2", "Normal 3", "Normal 4", "Normal 5",
            "Spear 1", "Spear 2", "Spear 3", "Spear 4", "Test"
        ]
        
        positions_match = SENSOR_POSITIONS == expected_positions
        labels_match = SENSOR_LABELS == expected_labels
        
        print(f"✓ 传感器位置匹配: {positions_match}")
        print(f"✓ 传感器标签匹配: {labels_match}")
        
        if positions_match and labels_match:
            print("✓ 完全符合参考图要求！")
        else:
            print("✗ 与参考图不完全匹配")
        
        # 测试GUI创建（不显示）
        print("\n6. 测试GUI组件创建...")
        try:
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口
            
            # 创建GUI实例但不运行
            app = ProfessionalVisualizerGUI()
            app.root.withdraw()  # 隐藏主窗口
            
            print("✓ GUI组件创建成功")
            
            # 测试数据更新
            app.current_data = test_data
            print("✓ 数据更新测试成功")
            
            # 清理
            app.root.destroy()
            root.destroy()
            
        except Exception as e:
            print(f"✗ GUI测试失败: {e}")
        
        print("\n" + "=" * 60)
        print("测试完成！专业可视化软件已准备就绪")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_visualizer_demo():
    """运行可视化演示"""
    print("\n启动专业可视化软件演示...")
    
    try:
        # 启动可视化软件
        process = subprocess.Popen([
            sys.executable, 
            "SerialDataVisualizer_Professional.py"
        ], cwd=current_dir)
        
        print("✓ 专业可视化软件已启动")
        print("✓ 界面特点:")
        print("  - 左侧: 3D坐标图显示前5个传感器位置数据")
        print("  - 右侧: 完整传感器参数列表 (Normal 1-5, Spear 1-4, Test)")
        print("  - 实时时间显示")
        print("  - 专业界面布局")
        print("  - 模拟数据生成 (与参考图数值范围一致)")
        
        print("\n按 Ctrl+C 停止演示...")
        
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n正在停止演示...")
            process.terminate()
            process.wait()
            print("✓ 演示已停止")
        
    except Exception as e:
        print(f"✗ 启动演示失败: {e}")

def main():
    """主函数"""
    print("专业可视化软件测试与演示")
    print("版权归属: 米醋电子工作室")
    print()
    
    # 运行测试
    if test_professional_visualizer():
        print("\n是否要运行可视化演示? (y/n): ", end="")
        try:
            choice = input().lower().strip()
            if choice in ['y', 'yes', '是', '']:
                run_visualizer_demo()
        except KeyboardInterrupt:
            print("\n测试结束")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
