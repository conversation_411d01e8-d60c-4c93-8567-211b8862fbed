#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口数据可视化软件 - 命令行版本
版权归属: 米醋电子工作室
创建日期: 2025-08-26
作者: <PERSON> (工程师)

这是一个简化的命令行版本，用于演示核心功能
"""

import sys
import os
import time
import threading
import signal
from datetime import datetime
from typing import List, Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False
    print("警告: pyserial 未安装，将使用模拟数据")

import numpy as np
from config.constants import SENSOR_POSITIONS, DATA_RANGES
from utils.logger import setup_logger
from utils.file_handler import DataFileHandler


class SerialDataVisualizerCLI:
    """串口数据可视化命令行版本"""
    
    def __init__(self):
        self.logger = setup_logger()
        self.file_handler = DataFileHandler()
        
        # 串口相关
        self.serial_port = None
        self.is_connected = False
        self.is_running = False
        
        # 数据相关
        self.current_data = [0.0] * 5
        self.data_history = []
        self.is_recording = False
        
        # 统计信息
        self.total_packets = 0
        self.valid_packets = 0
        self.start_time = None
        
        # 线程
        self.read_thread = None
        self.display_thread = None
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        
        self.logger.info("串口数据可视化CLI初始化完成")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n\n正在关闭程序...")
        self.stop()
        sys.exit(0)
    
    def scan_ports(self) -> List[str]:
        """扫描可用串口"""
        if not SERIAL_AVAILABLE:
            return ["COM1", "COM2", "COM3"]  # 模拟端口
        
        try:
            ports = serial.tools.list_ports.comports()
            return [port.device for port in ports]
        except Exception as e:
            self.logger.error(f"扫描串口失败: {e}")
            return []
    
    def connect(self, port: str, baudrate: int = 115200) -> bool:
        """连接串口"""
        if not SERIAL_AVAILABLE:
            print(f"模拟连接到 {port}, 波特率: {baudrate}")
            self.is_connected = True
            return True
        
        try:
            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=8,
                stopbits=1,
                parity='N',
                timeout=1.0
            )
            
            if self.serial_port.is_open:
                self.is_connected = True
                self.logger.info(f"串口连接成功: {port}")
                return True
            else:
                raise Exception("串口打开失败")
                
        except Exception as e:
            self.logger.error(f"串口连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        self.is_running = False
        
        if self.serial_port and self.serial_port.is_open:
            try:
                self.serial_port.close()
                self.logger.info("串口已断开")
            except Exception as e:
                self.logger.error(f"断开串口时出错: {e}")
        
        self.is_connected = False
        self.serial_port = None
    
    def parse_data(self, raw_data: str) -> Optional[List[float]]:
        """解析数据"""
        try:
            # 尝试不同的分隔符
            for delimiter in [',', ' ', ';']:
                if delimiter in raw_data:
                    parts = raw_data.split(delimiter)
                    break
            else:
                parts = raw_data.split()
            
            # 过滤空字符串并转换为浮点数
            values = []
            for part in parts:
                part = part.strip()
                if part:
                    values.append(float(part))
            
            if len(values) >= 10:
                self.total_packets += 1
                self.valid_packets += 1
                return values[:5]  # 只返回前5个传感器数值
            else:
                self.total_packets += 1
                return None
                
        except Exception as e:
            self.total_packets += 1
            self.logger.error(f"数据解析失败: {e}")
            return None
    
    def generate_mock_data(self) -> List[float]:
        """生成模拟数据"""
        import random
        base_values = [1000, 1500, 2000, 2500, 3000]
        return [base + random.randint(-200, 200) for base in base_values]
    
    def read_data_loop(self):
        """数据读取循环"""
        buffer = ""
        
        while self.is_running:
            try:
                if SERIAL_AVAILABLE and self.serial_port and self.serial_port.is_open:
                    # 真实串口数据读取
                    if self.serial_port.in_waiting > 0:
                        data = self.serial_port.read(self.serial_port.in_waiting).decode('utf-8', errors='ignore')
                        buffer += data
                        
                        # 查找完整的数据行
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            line = line.strip()
                            if line:
                                parsed_data = self.parse_data(line)
                                if parsed_data:
                                    self.current_data = parsed_data
                                    if self.is_recording:
                                        self.data_history.append({
                                            'timestamp': datetime.now(),
                                            'data': parsed_data.copy()
                                        })
                else:
                    # 模拟数据
                    self.current_data = self.generate_mock_data()
                    self.total_packets += 1
                    self.valid_packets += 1
                    
                    if self.is_recording:
                        self.data_history.append({
                            'timestamp': datetime.now(),
                            'data': self.current_data.copy()
                        })
                
                time.sleep(0.1)  # 100ms间隔
                
            except Exception as e:
                if self.is_running:
                    self.logger.error(f"读取数据时出错: {e}")
                break
    
    def display_loop(self):
        """显示循环"""
        while self.is_running:
            try:
                self.clear_screen()
                self.display_header()
                self.display_sensor_data()
                self.display_3d_visualization()
                self.display_statistics()
                self.display_controls()
                
                time.sleep(0.5)  # 2Hz刷新率
                
            except Exception as e:
                self.logger.error(f"显示更新时出错: {e}")
                break
    
    def clear_screen(self):
        """清屏"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def display_header(self):
        """显示标题"""
        print("=" * 80)
        print("                    串口数据可视化软件 - 命令行版本")
        print("                        版权归属: 米醋电子工作室")
        print("=" * 80)
        
        # 连接状态
        status = "已连接" if self.is_connected else "未连接"
        color = "\033[92m" if self.is_connected else "\033[91m"  # 绿色/红色
        print(f"连接状态: {color}{status}\033[0m")
        
        # 记录状态
        if self.is_recording:
            print("\033[93m● 正在记录数据\033[0m")
        
        print()
    
    def display_sensor_data(self):
        """显示传感器数据"""
        print("实时传感器数据:")
        print("-" * 40)
        
        current_time = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        print(f"时间戳: {current_time}")
        print()
        
        for i, (value, pos) in enumerate(zip(self.current_data, SENSOR_POSITIONS)):
            # 根据数值选择颜色
            if value < 1000:
                color = "\033[92m"  # 绿色
            elif value < 2000:
                color = "\033[93m"  # 黄色
            elif value < 3000:
                color = "\033[95m"  # 紫色
            else:
                color = "\033[91m"  # 红色
            
            print(f"传感器{i+1} [{pos[0]:.1f}, {pos[1]:.1f}]: {color}{value:7.2f}\033[0m")
        
        print()
    
    def display_3d_visualization(self):
        """显示3D可视化 (ASCII艺术)"""
        print("3D可视化 (俯视图):")
        print("-" * 40)
        
        # 创建一个简单的ASCII网格
        grid = [[' ' for _ in range(21)] for _ in range(11)]
        
        # 绘制网格线
        for i in range(0, 11, 2):
            for j in range(21):
                grid[i][j] = '·'
        for i in range(11):
            for j in range(0, 21, 4):
                grid[i][j] = '·'
        
        # 绘制传感器位置
        sensor_chars = ['①', '②', '③', '④', '⑤']
        for i, (value, pos) in enumerate(zip(self.current_data, SENSOR_POSITIONS)):
            x = int(pos[0] * 20)
            y = int(pos[1] * 10)
            
            # 根据数值选择显示字符
            if value > 2500:
                char = '●'  # 高值
            elif value > 1500:
                char = '◐'  # 中值
            else:
                char = '○'  # 低值
            
            if 0 <= y < 11 and 0 <= x < 21:
                grid[y][x] = char
        
        # 输出网格
        for row in grid:
            print(''.join(row))
        
        print("\n图例: ○ 低值  ◐ 中值  ● 高值")
        print()
    
    def display_statistics(self):
        """显示统计信息"""
        print("统计信息:")
        print("-" * 40)
        
        success_rate = (self.valid_packets / self.total_packets * 100) if self.total_packets > 0 else 0
        
        print(f"总数据包: {self.total_packets}")
        print(f"有效数据: {self.valid_packets}")
        print(f"错误数据: {self.total_packets - self.valid_packets}")
        print(f"成功率: {success_rate:.1f}%")
        
        if self.start_time:
            elapsed = datetime.now() - self.start_time
            print(f"运行时间: {str(elapsed).split('.')[0]}")
        
        if self.is_recording:
            print(f"已记录: {len(self.data_history)} 条数据")
        
        print()
    
    def display_controls(self):
        """显示控制说明"""
        print("控制说明:")
        print("-" * 40)
        print("Ctrl+C: 退出程序")
        print("在另一个终端中运行以下命令进行控制:")
        print("  python -c \"import os; os.system('echo r > control.txt')\"  # 开始/停止记录")
        print("  python -c \"import os; os.system('echo e > control.txt')\"  # 导出数据")
        print()
    
    def check_control_commands(self):
        """检查控制命令"""
        try:
            if os.path.exists('control.txt'):
                with open('control.txt', 'r') as f:
                    command = f.read().strip().lower()
                
                if command == 'r':
                    self.toggle_recording()
                elif command == 'e':
                    self.export_data()
                
                os.remove('control.txt')
        except:
            pass
    
    def toggle_recording(self):
        """切换记录状态"""
        self.is_recording = not self.is_recording
        if self.is_recording:
            self.data_history.clear()
            self.logger.info("开始数据记录")
        else:
            self.logger.info(f"停止数据记录，共记录 {len(self.data_history)} 条数据")
    
    def export_data(self):
        """导出数据"""
        if not self.data_history:
            self.logger.warning("没有数据可导出")
            return
        
        try:
            # 转换数据格式
            export_data = []
            for record in self.data_history:
                row = {
                    'timestamp': record['timestamp'].isoformat(),
                    'sensor1': record['data'][0],
                    'sensor2': record['data'][1],
                    'sensor3': record['data'][2],
                    'sensor4': record['data'][3],
                    'sensor5': record['data'][4],
                }
                export_data.append(row)
            
            # 保存到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"sensor_data_{timestamp}.csv"
            filepath = self.file_handler.save_to_csv(export_data, filename)
            
            self.logger.info(f"数据已导出到: {filepath}")
            
        except Exception as e:
            self.logger.error(f"导出数据失败: {e}")
    
    def start(self, port: str = None, baudrate: int = 115200):
        """启动程序"""
        print("启动串口数据可视化软件...")
        
        # 扫描端口
        available_ports = self.scan_ports()
        print(f"可用端口: {', '.join(available_ports) if available_ports else '无'}")
        
        # 连接串口
        if port is None and available_ports:
            port = available_ports[0]
        elif port is None:
            port = "COM1"  # 默认端口
        
        if self.connect(port, baudrate):
            print(f"已连接到 {port}")
        else:
            print(f"连接失败，使用模拟数据")
        
        # 启动线程
        self.is_running = True
        self.start_time = datetime.now()
        
        self.read_thread = threading.Thread(target=self.read_data_loop, daemon=True)
        self.display_thread = threading.Thread(target=self.display_loop, daemon=True)
        
        self.read_thread.start()
        self.display_thread.start()
        
        # 主循环
        try:
            while self.is_running:
                self.check_control_commands()
                time.sleep(0.1)
        except KeyboardInterrupt:
            pass
        
        self.stop()
    
    def stop(self):
        """停止程序"""
        self.is_running = False
        self.disconnect()
        
        if self.data_history:
            print(f"\n自动导出 {len(self.data_history)} 条记录的数据...")
            self.export_data()
        
        print("程序已退出")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='串口数据可视化软件 - 命令行版本')
    parser.add_argument('--port', '-p', type=str, help='串口端口 (如: COM1)')
    parser.add_argument('--baudrate', '-b', type=int, default=115200, help='波特率 (默认: 115200)')
    
    args = parser.parse_args()
    
    visualizer = SerialDataVisualizerCLI()
    visualizer.start(args.port, args.baudrate)


if __name__ == "__main__":
    main()
