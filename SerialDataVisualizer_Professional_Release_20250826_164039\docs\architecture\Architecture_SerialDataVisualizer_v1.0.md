# 系统架构设计文档 - 串口数据可视化软件

## 1. 文档信息
- **项目名称**: 串口数据可视化软件
- **版本**: v1.0
- **创建日期**: 2025-08-26
- **负责人**: Bob (架构师)
- **版权归属**: 米醋电子工作室

## 2. 架构概览

### 2.1 架构原则
- **单体应用**: 独立运行的桌面应用程序
- **模块化设计**: 松耦合的功能模块
- **事件驱动**: 基于Qt信号槽机制的异步通信
- **线程安全**: 串口通信和UI渲染分离

### 2.2 技术栈选型

#### 核心框架
- **GUI框架**: PyQt5 (稳定性和兼容性考虑)
- **3D渲染**: PyOpenGL + OpenGL 3.3 Core Profile
- **串口通信**: PySerial 3.5+
- **数据处理**: NumPy 1.21+
- **打包工具**: PyInstaller (生成独立可执行文件)

#### 开发环境
- **Python版本**: 3.8+ (兼容性最佳)
- **IDE**: 支持context7环境
- **测试框架**: pytest + playwright (UI自动化测试)

## 3. 系统架构设计

### 3.1 整体架构图

```
┌─────────────────────────────────────────────────────────┐
│                   主应用程序 (MainApp)                    │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │   UI层      │  │  业务逻辑层   │  │   数据层     │      │
│  │             │  │             │  │             │      │
│  │ MainWindow  │◄─┤ Controller  │◄─┤ DataManager │      │
│  │ 3DWidget    │  │ SerialMgr   │  │ FileHandler │      │
│  │ ControlPanel│  │ DataParser  │  │ ConfigMgr   │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
├─────────────────────────────────────────────────────────┤
│                   底层服务层                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │ 串口通信线程  │  │ 数据处理线程  │  │ 渲染线程     │      │
│  │ SerialThread│  │ProcessThread│  │RenderThread │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
```

### 3.2 核心模块设计

#### 3.2.1 主应用程序模块 (MainApp)
```python
class MainApp(QApplication):
    """主应用程序类，负责应用程序生命周期管理"""
    - 初始化应用程序
    - 管理全局配置
    - 处理应用程序退出
    - 异常处理和日志记录
```

#### 3.2.2 用户界面层 (UI Layer)

**MainWindow - 主窗口**
```python
class MainWindow(QMainWindow):
    """主窗口类，负责整体界面布局"""
    - 菜单栏: 文件、设置、帮助
    - 工具栏: 连接、断开、暂停、导出
    - 状态栏: 连接状态、数据统计
    - 中央区域: 3D可视化 + 控制面板
```

**OpenGL3DWidget - 3D可视化组件**
```python
class OpenGL3DWidget(QOpenGLWidget):
    """3D可视化核心组件"""
    - OpenGL渲染环境初始化
    - 3D坐标系绘制
    - 传感器数据点渲染
    - 鼠标交互控制 (旋转、缩放、平移)
    - 实时数据更新
```

**ControlPanel - 控制面板**
```python
class ControlPanel(QWidget):
    """右侧控制面板"""
    - 串口参数配置 (端口、波特率)
    - 连接控制按钮
    - 实时数据显示
    - 数据记录控制
    - 视图控制选项
```

#### 3.2.3 业务逻辑层 (Business Layer)

**SerialManager - 串口管理器**
```python
class SerialManager(QObject):
    """串口通信管理器"""
    - 串口端口扫描和检测
    - 连接建立和断开
    - 数据接收和缓冲
    - 连接状态监控
    - 自动重连机制
    
    信号:
    - data_received(bytes)  # 数据接收信号
    - connection_changed(bool)  # 连接状态变化
    - error_occurred(str)  # 错误信号
```

**DataParser - 数据解析器**
```python
class DataParser(QObject):
    """数据解析和处理器"""
    - 10个数据包解析
    - 传感器数据提取
    - 数据格式验证
    - 异常数据过滤
    
    信号:
    - sensor_data_ready(list)  # 传感器数据就绪
    - parse_error(str)  # 解析错误
```

**DataController - 数据控制器**
```python
class DataController(QObject):
    """数据流控制器"""
    - 协调串口管理器和数据解析器
    - 管理数据流状态
    - 实现数据缓存和队列
    - 处理数据记录和导出
```

#### 3.2.4 数据层 (Data Layer)

**DataManager - 数据管理器**
```python
class DataManager(QObject):
    """数据存储和管理"""
    - 实时数据缓存
    - 历史数据存储
    - CSV文件导出
    - 数据统计计算
```

**ConfigManager - 配置管理器**
```python
class ConfigManager:
    """应用程序配置管理"""
    - 串口参数配置
    - 界面布局配置
    - 用户偏好设置
    - 配置文件读写
```

### 3.3 线程架构设计

#### 3.3.1 主线程 (UI Thread)
- 负责UI渲染和用户交互
- 处理Qt事件循环
- 响应用户操作

#### 3.3.2 串口通信线程 (Serial Thread)
```python
class SerialThread(QThread):
    """串口通信专用线程"""
    - 异步串口数据接收
    - 避免阻塞UI线程
    - 实现数据缓冲机制
    - 处理串口异常
```

#### 3.3.3 数据处理线程 (Data Processing Thread)
```python
class DataProcessingThread(QThread):
    """数据处理专用线程"""
    - 数据解析和验证
    - 数据格式转换
    - 统计计算
    - 文件I/O操作
```

## 4. 数据流设计

### 4.1 数据流向图
```
单片机 → 串口 → SerialThread → DataParser → DataController → UI组件
                     ↓
                 DataManager → 文件存储
```

### 4.2 数据格式定义

#### 4.2.1 串口数据格式
```
数据包格式: [sensor1, sensor2, sensor3, sensor4, sensor5, ext1, ext2, ext3, ext4, ext5]
- sensor1-5: 传感器数值 (float)
- ext1-5: 扩展数据 (预留)
- 分隔符: 逗号或空格
- 结束符: \n 或 \r\n
```

#### 4.2.2 内部数据结构
```python
@dataclass
class SensorData:
    timestamp: datetime
    sensor_values: List[float]  # 5个传感器数值
    extended_data: List[float]  # 5个扩展数据
    positions: List[Tuple[float, float]]  # 传感器位置坐标
```

## 5. 3D渲染架构

### 5.1 OpenGL渲染管线
```
顶点数据 → 顶点着色器 → 几何处理 → 片段着色器 → 帧缓冲
```

### 5.2 渲染对象设计
- **坐标系**: 3D坐标轴和网格
- **传感器点**: 球体几何体，颜色映射数值
- **数据平面**: 可选的数据插值平面
- **文本标签**: 坐标轴标签和数值显示

### 5.3 性能优化策略
- 使用VBO (Vertex Buffer Objects) 减少数据传输
- 实现视锥体裁剪减少渲染负载
- 使用LOD (Level of Detail) 技术
- 帧率限制和垂直同步

## 6. 错误处理和异常管理

### 6.1 异常分类
- **串口异常**: 连接失败、数据传输错误
- **数据异常**: 格式错误、数值超范围
- **渲染异常**: OpenGL错误、显卡兼容性
- **文件异常**: 读写权限、磁盘空间

### 6.2 错误处理策略
- 分层异常处理机制
- 用户友好的错误提示
- 自动恢复和重试机制
- 详细的错误日志记录

## 7. 性能要求和优化

### 7.1 性能指标
- **数据接收**: 支持115200波特率无丢包
- **UI响应**: 界面操作响应时间 < 100ms
- **3D渲染**: 维持30fps以上刷新率
- **内存使用**: 运行时内存 < 100MB

### 7.2 优化策略
- 数据缓冲和批处理
- 异步处理和多线程
- OpenGL硬件加速
- 内存池和对象复用

## 8. 部署和打包

### 8.1 打包策略
- 使用PyInstaller生成单文件可执行程序
- 包含所有依赖库和资源文件
- 支持Windows 10/11 64位系统
- 文件大小控制在50MB以内

### 8.2 安装要求
- 无需Python环境
- 无需额外安装依赖
- 支持便携式运行
- 自动创建配置文件

## 9. 技术决策记录 (ADR)

### ADR-001: GUI框架选择
- **决策**: 选择PyQt5而非PyQt6
- **理由**: PyQt5生态更成熟，第三方库兼容性更好，打包后体积更小
- **风险**: PyQt5长期支持问题
- **缓解**: 项目完成后可考虑迁移到PyQt6

### ADR-002: 3D渲染技术选择
- **决策**: 使用PyOpenGL + 原生OpenGL
- **理由**: 性能最佳，可控性强，跨平台兼容性好
- **替代方案**: Qt3D (集成度高但性能较差)
- **风险**: 开发复杂度较高
- **缓解**: 使用成熟的OpenGL封装库

### ADR-003: 打包方案选择
- **决策**: 使用PyInstaller单文件打包
- **理由**: 用户使用最简单，无需安装Python环境
- **替代方案**: cx_Freeze, py2exe
- **风险**: 启动速度较慢，文件体积较大
- **缓解**: 优化导入模块，使用UPX压缩

## 10. 开发规范

### 10.1 代码规范
- 遵循PEP 8 Python代码规范
- 使用类型注解提高代码可读性
- 单一职责原则，每个类职责明确
- 接口隔离，模块间松耦合

### 10.2 文件组织结构
```
serial_visualizer/
├── main.py                 # 应用程序入口
├── config/
│   ├── __init__.py
│   ├── settings.py         # 配置管理
│   └── constants.py        # 常量定义
├── core/
│   ├── __init__.py
│   ├── serial_manager.py   # 串口管理
│   ├── data_parser.py      # 数据解析
│   └── data_controller.py  # 数据控制
├── ui/
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   ├── opengl_widget.py    # 3D组件
│   └── control_panel.py    # 控制面板
├── utils/
│   ├── __init__.py
│   ├── logger.py           # 日志工具
│   └── file_handler.py     # 文件处理
├── resources/
│   ├── icons/              # 图标资源
│   ├── shaders/            # OpenGL着色器
│   └── config.ini          # 默认配置
└── tests/
    ├── __init__.py
    ├── test_serial.py      # 串口测试
    ├── test_parser.py      # 解析测试
    └── test_ui.py          # UI测试
```

### 10.3 测试策略
- **单元测试**: 覆盖核心业务逻辑
- **集成测试**: 测试模块间协作
- **UI测试**: 使用playwright自动化测试
- **性能测试**: 长时间运行稳定性测试

---

**架构设计完成时间**: 2025-08-26
**技术债务**: 无
**下一步**: 等待Mike指令开始详细开发实现
