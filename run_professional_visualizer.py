#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业可视化软件启动脚本
版权归属: 米醋电子工作室
创建日期: 2025-08-26
作者: <PERSON> (工程师)

简化的启动脚本，确保软件正常运行
"""

import sys
import os
import subprocess
import time

def check_dependencies():
    """检查依赖库"""
    print("检查依赖库...")
    
    required_modules = [
        ('tkinter', 'GUI框架'),
        ('matplotlib', '绘图库'),
        ('numpy', '数值计算库')
    ]
    
    missing_modules = []
    
    for module_name, description in required_modules:
        try:
            __import__(module_name)
            print(f"✓ {module_name} ({description}) - 已安装")
        except ImportError:
            print(f"✗ {module_name} ({description}) - 未安装")
            missing_modules.append(module_name)
    
    # 检查可选模块
    try:
        import serial
        print("✓ pyserial (串口通信) - 已安装")
    except ImportError:
        print("⚠ pyserial (串口通信) - 未安装 (将使用模拟数据)")
    
    if missing_modules:
        print(f"\n缺少必需的依赖库: {', '.join(missing_modules)}")
        print("请安装缺少的库后重试")
        return False
    
    print("✓ 所有必需依赖库已安装")
    return True

def run_visualizer():
    """运行专业可视化软件"""
    print("\n启动专业可视化软件...")
    print("=" * 50)
    
    try:
        # 获取当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        visualizer_path = os.path.join(current_dir, "SerialDataVisualizer_Professional.py")
        
        if not os.path.exists(visualizer_path):
            print(f"✗ 找不到可视化软件文件: {visualizer_path}")
            return False
        
        print("✓ 找到可视化软件文件")
        print("✓ 正在启动...")
        
        # 启动可视化软件
        process = subprocess.Popen([
            sys.executable, 
            visualizer_path
        ], cwd=current_dir)
        
        print("✓ 专业可视化软件已启动!")
        print("\n软件特性:")
        print("  🎯 完全按照参考图设计")
        print("  📊 左侧: 3D坐标图 (显示前5个传感器)")
        print("  📋 右侧: 传感器参数列表 (Normal 1-5, Spear 1-4, Test)")
        print("  ⏰ 实时时间显示")
        print("  🎨 专业界面布局")
        print("  📡 模拟数据生成 (与参考图数值范围一致)")
        print("  💾 数据记录和导出功能")
        
        print("\n操作说明:")
        print("  1. 点击'连接'按钮开始接收数据")
        print("  2. 点击'开始记录'保存数据")
        print("  3. 点击'导出数据'保存为CSV文件")
        print("  4. 关闭窗口退出程序")
        
        print(f"\n进程ID: {process.pid}")
        print("按 Ctrl+C 可以从这里停止监控...")
        
        try:
            # 等待进程结束
            process.wait()
            print("\n✓ 可视化软件已正常退出")
        except KeyboardInterrupt:
            print("\n正在停止可视化软件...")
            process.terminate()
            try:
                process.wait(timeout=5)
                print("✓ 可视化软件已停止")
            except subprocess.TimeoutExpired:
                process.kill()
                print("✓ 可视化软件已强制停止")
        
        return True
        
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("专业可视化软件启动器")
    print("版权归属: 米醋电子工作室")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装缺少的依赖库:")
        print("pip install matplotlib numpy")
        print("pip install pyserial  # 可选，用于真实串口通信")
        return 1
    
    # 运行可视化软件
    if run_visualizer():
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
