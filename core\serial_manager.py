#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口管理器模块
版权归属: 米醋电子工作室
"""

import serial
import serial.tools.list_ports
import threading
import time
from typing import List, Optional, Callable
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from utils.logger import get_logger
from config.constants import SERIAL_BAUDRATES


class SerialManager(QObject):
    """串口通信管理器"""
    
    # 信号定义
    data_received = pyqtSignal(bytes)  # 数据接收信号
    connection_changed = pyqtSignal(bool)  # 连接状态变化信号
    error_occurred = pyqtSignal(str)  # 错误信号
    port_list_updated = pyqtSignal(list)  # 端口列表更新信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger()
        
        # 串口对象
        self.serial_port: Optional[serial.Serial] = None
        self.is_connected = False
        self.is_running = False
        
        # 连接参数
        self.port_name = "COM1"
        self.baudrate = 115200
        self.databits = 8
        self.stopbits = 1
        self.parity = 'N'
        self.timeout = 1.0
        
        # 读取线程
        self.read_thread: Optional[threading.Thread] = None
        self.read_lock = threading.Lock()
        
        # 自动重连
        self.auto_reconnect = True
        self.reconnect_timer = QTimer()
        self.reconnect_timer.timeout.connect(self._try_reconnect)
        
        # 端口扫描定时器
        self.port_scan_timer = QTimer()
        self.port_scan_timer.timeout.connect(self.scan_ports)
        self.port_scan_timer.start(2000)  # 每2秒扫描一次
        
        self.logger.info("串口管理器初始化完成")
    
    def scan_ports(self) -> List[str]:
        """扫描可用串口"""
        try:
            ports = serial.tools.list_ports.comports()
            port_list = [port.device for port in ports]
            self.port_list_updated.emit(port_list)
            return port_list
        except Exception as e:
            self.logger.error(f"扫描串口失败: {e}")
            return []
    
    def set_connection_params(self, port: str, baudrate: int, databits: int = 8, 
                            stopbits: int = 1, parity: str = 'N', timeout: float = 1.0):
        """设置连接参数"""
        self.port_name = port
        self.baudrate = baudrate
        self.databits = databits
        self.stopbits = stopbits
        self.parity = parity
        self.timeout = timeout
        
        self.logger.info(f"串口参数已设置: {port}, {baudrate}, {databits}{parity}{stopbits}")
    
    def connect(self) -> bool:
        """连接串口"""
        if self.is_connected:
            self.logger.warning("串口已连接")
            return True
        
        try:
            # 创建串口对象
            self.serial_port = serial.Serial(
                port=self.port_name,
                baudrate=self.baudrate,
                bytesize=self.databits,
                stopbits=self.stopbits,
                parity=self.parity,
                timeout=self.timeout
            )
            
            if self.serial_port.is_open:
                self.is_connected = True
                self.is_running = True
                
                # 启动读取线程
                self.read_thread = threading.Thread(target=self._read_data, daemon=True)
                self.read_thread.start()
                
                # 停止重连定时器
                self.reconnect_timer.stop()
                
                self.connection_changed.emit(True)
                self.logger.info(f"串口连接成功: {self.port_name}")
                return True
            else:
                raise Exception("串口打开失败")
                
        except Exception as e:
            error_msg = f"串口连接失败: {e}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            
            # 启动自动重连
            if self.auto_reconnect:
                self.reconnect_timer.start(5000)  # 5秒后重试
            
            return False
    
    def disconnect(self):
        """断开串口连接"""
        self.is_running = False
        
        # 停止重连定时器
        self.reconnect_timer.stop()
        
        if self.serial_port and self.serial_port.is_open:
            try:
                self.serial_port.close()
                self.logger.info("串口已断开")
            except Exception as e:
                self.logger.error(f"断开串口时出错: {e}")
        
        # 等待读取线程结束
        if self.read_thread and self.read_thread.is_alive():
            self.read_thread.join(timeout=2.0)
        
        self.is_connected = False
        self.serial_port = None
        self.connection_changed.emit(False)
    
    def _read_data(self):
        """数据读取线程函数"""
        buffer = b''
        
        while self.is_running and self.serial_port and self.serial_port.is_open:
            try:
                with self.read_lock:
                    if self.serial_port.in_waiting > 0:
                        data = self.serial_port.read(self.serial_port.in_waiting)
                        if data:
                            buffer += data
                            
                            # 查找完整的数据行
                            while b'\n' in buffer:
                                line, buffer = buffer.split(b'\n', 1)
                                if line.strip():
                                    self.data_received.emit(line.strip())
                
                time.sleep(0.01)  # 避免CPU占用过高
                
            except Exception as e:
                if self.is_running:  # 只有在运行状态下才报告错误
                    error_msg = f"读取数据时出错: {e}"
                    self.logger.error(error_msg)
                    self.error_occurred.emit(error_msg)
                    
                    # 连接断开，尝试重连
                    self.is_connected = False
                    self.connection_changed.emit(False)
                    
                    if self.auto_reconnect:
                        self.reconnect_timer.start(5000)
                    
                break
    
    def _try_reconnect(self):
        """尝试重新连接"""
        if not self.is_connected:
            self.logger.info("尝试重新连接串口...")
            self.connect()
    
    def send_data(self, data: bytes) -> bool:
        """发送数据"""
        if not self.is_connected or not self.serial_port:
            self.logger.warning("串口未连接，无法发送数据")
            return False
        
        try:
            with self.read_lock:
                self.serial_port.write(data)
                self.serial_port.flush()
            
            self.logger.debug(f"发送数据: {data}")
            return True
            
        except Exception as e:
            error_msg = f"发送数据失败: {e}"
            self.logger.error(error_msg)
            self.error_occurred.emit(error_msg)
            return False
    
    def get_status(self) -> dict:
        """获取串口状态信息"""
        return {
            'connected': self.is_connected,
            'port': self.port_name,
            'baudrate': self.baudrate,
            'running': self.is_running
        }
