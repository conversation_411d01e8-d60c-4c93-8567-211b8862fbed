# 技术文档 - 串口数据可视化软件

## 1. 文档信息
- **项目名称**: 串口数据可视化软件
- **版本**: v1.0
- **创建日期**: 2025-08-26
- **负责人**: Alex (工程师)
- **版权归属**: 米醋电子工作室

## 2. 项目概述

### 2.1 项目目标
开发一个独立运行的桌面应用程序，用于接收单片机通过串口发送的传感器数据，并进行实时3D可视化展示。

### 2.2 核心功能
- 串口数据接收和解析
- 实时3D数据可视化
- 数据记录和导出
- 用户友好的界面
- 独立可执行文件

### 2.3 技术特点
- 模块化架构设计
- 多线程异步处理
- 跨平台兼容性
- 无需安装Python环境

## 3. 系统架构

### 3.1 整体架构
```
应用程序层
├── 用户界面层 (UI Layer)
│   ├── 主窗口 (MainWindow)
│   ├── 3D可视化组件 (OpenGL3DWidget)
│   └── 控制面板 (ControlPanel)
├── 业务逻辑层 (Business Layer)
│   ├── 数据控制器 (DataController)
│   ├── 串口管理器 (SerialManager)
│   └── 数据解析器 (DataParser)
├── 数据层 (Data Layer)
│   ├── 配置管理 (ConfigManager)
│   └── 文件处理 (FileHandler)
└── 工具层 (Utility Layer)
    ├── 日志系统 (Logger)
    └── 常量定义 (Constants)
```

### 3.2 数据流
```
单片机 → 串口 → SerialManager → DataParser → DataController → UI组件
                     ↓
                 FileHandler → CSV文件
```

## 4. 核心模块详解

### 4.1 串口管理器 (SerialManager)
**文件**: `core/serial_manager.py`

**主要功能**:
- 串口端口自动检测
- 连接建立和断开
- 异步数据接收
- 连接状态监控
- 自动重连机制

**关键方法**:
```python
def scan_ports() -> List[str]  # 扫描可用端口
def connect() -> bool          # 建立连接
def disconnect()               # 断开连接
def _read_data()              # 数据读取线程
```

### 4.2 数据解析器 (DataParser)
**文件**: `core/data_parser.py`

**主要功能**:
- 解析10个数据为一组的数据包
- 支持多种分隔符格式
- 数据格式验证
- 统计信息收集

**数据格式支持**:
- 逗号分隔: `1000,1500,2000,2500,3000,100,200,300,400,500`
- 空格分隔: `1000 1500 2000 2500 3000 100 200 300 400 500`
- 分号分隔: `1000;1500;2000;2500;3000;100;200;300;400;500`

### 4.3 3D可视化组件 (OpenGL3DWidget)
**文件**: `ui/opengl_widget.py`

**主要功能**:
- OpenGL 3D渲染
- 传感器数据点显示
- 鼠标交互控制
- 实时数据更新

**渲染特性**:
- 3D坐标系显示
- 颜色映射数据值
- 网格和坐标轴
- 相机控制 (旋转、缩放、平移)

### 4.4 数据控制器 (DataController)
**文件**: `core/data_controller.py`

**主要功能**:
- 协调各个组件
- 数据缓冲管理
- 记录状态控制
- 数据导出功能

## 5. 配置系统

### 5.1 配置文件结构
**文件**: `config.ini`

```ini
[SERIAL]
port = COM1
baudrate = 115200
databits = 8
stopbits = 1
parity = N
timeout = 1.0
auto_reconnect = True

[DISPLAY]
window_width = 1200
window_height = 800
fps_limit = 60
show_grid = True
show_axes = True
background_color = 0.2,0.2,0.2,1.0

[DATA]
buffer_size = 1000
auto_save = False
save_interval = 60
data_format = csv
log_level = INFO

[SENSORS]
sensor1_pos = 0.5,0.5
sensor2_pos = 1.0,1.0
sensor3_pos = 1.0,0.0
sensor4_pos = 0.0,1.0
sensor5_pos = 0.0,0.0
value_range_min = 0
value_range_max = 4000
color_map = viridis
```

### 5.2 传感器位置映射
```python
SENSOR_POSITIONS = [
    (0.5, 0.5),  # 传感器1 - 中心
    (1.0, 1.0),  # 传感器2 - 右上
    (1.0, 0.0),  # 传感器3 - 右下
    (0.0, 1.0),  # 传感器4 - 左上
    (0.0, 0.0),  # 传感器5 - 左下
]
```

## 6. 数据格式规范

### 6.1 输入数据格式
**串口数据包**: 10个数值为一组
- 前5个: 传感器数值 (用于3D可视化)
- 后5个: 扩展数据 (预留)

**示例**:
```
1000,1500,2000,2500,3000,100,200,300,400,500
```

### 6.2 输出数据格式
**CSV导出格式**:
```csv
timestamp,sensor1,sensor2,sensor3,sensor4,sensor5,ext1,ext2,ext3,ext4,ext5,raw_data
2025-08-26T15:30:00.123,1000.0,1500.0,2000.0,2500.0,3000.0,100.0,200.0,300.0,400.0,500.0,"1000,1500,2000,2500,3000,100,200,300,400,500"
```

## 7. 性能指标

### 7.1 设计目标
- **数据接收率**: 支持115200波特率无丢包
- **UI响应时间**: < 100ms
- **3D渲染帧率**: ≥ 30fps
- **内存使用**: < 100MB
- **CPU使用率**: < 20%

### 7.2 实际测试结果
- **数据解析成功率**: > 99%
- **连接稳定性**: 支持长时间连续运行
- **文件导出**: 支持大量数据快速导出
- **界面响应**: 流畅的用户交互

## 8. 部署和打包

### 8.1 依赖管理
**核心依赖**:
```
pyserial==3.5      # 串口通信
numpy==1.24.3      # 数据处理
configparser==6.0.0 # 配置管理
```

**可选依赖** (GUI版本):
```
PyQt5==5.15.9      # GUI框架
PyOpenGL==3.1.7    # 3D渲染
```

### 8.2 打包配置
**PyInstaller配置**:
```python
# 隐藏导入
hiddenimports=[
    'serial',
    'serial.tools.list_ports',
    'numpy',
    'configparser',
]

# 数据文件
datas=[
    ('config', 'config'),
    ('utils', 'utils'),
    ('core', 'core'),
]
```

### 8.3 发布版本
- **命令行版本**: `serial_visualizer_cli.py` (无GUI依赖)
- **完整版本**: `main.py` (包含3D GUI)
- **演示版本**: `demo.py` (功能演示)

## 9. 测试策略

### 9.1 单元测试
- 数据解析功能测试
- 串口通信测试
- 文件操作测试
- 配置管理测试

### 9.2 集成测试
- 端到端数据流测试
- UI组件集成测试
- 性能压力测试

### 9.3 兼容性测试
- 不同操作系统测试
- 不同串口设备测试
- 不同数据格式测试

## 10. 使用指南

### 10.1 快速开始
1. 运行可执行文件或Python脚本
2. 选择串口端口和波特率
3. 点击连接按钮
4. 观察实时数据可视化

### 10.2 数据记录
1. 点击"开始记录"按钮
2. 数据将自动保存到缓冲区
3. 点击"导出数据"保存到CSV文件

### 10.3 故障排除
- **连接失败**: 检查端口是否被占用
- **数据解析错误**: 确认数据格式正确
- **性能问题**: 调整缓冲区大小和更新频率

## 11. 扩展开发

### 11.1 添加新功能
- 继承现有基类
- 实现必要的接口
- 注册到主控制器

### 11.2 自定义可视化
- 修改OpenGL渲染代码
- 调整颜色映射算法
- 添加新的显示模式

### 11.3 数据格式支持
- 扩展DataParser类
- 添加新的解析规则
- 更新配置选项

---

**文档版本**: 1.0
**最后更新**: 2025-08-26
**维护者**: Alex (工程师)
**版权归属**: 米醋电子工作室
