# 串口数据可视化软件 - 专业版

版权归属: 米醋电子工作室
版本: v1.1.0 Professional
发布日期: 2025-08-26

## 快速开始

### 方式一: 双击启动（推荐）
1. 双击 "启动程序.bat"
2. 或直接双击 "SerialDataVisualizer_Professional.exe"

### 方式二: 演示模式（无需串口设备）
1. 双击 "演示模式.bat"
2. 软件将使用模拟数据运行

## 软件特性

✅ 完全按照用户参考图设计
✅ 左侧3D坐标图显示前5个传感器
✅ 右侧传感器参数面板（按颜色分类）
✅ 实时时间显示
✅ 数据记录和导出功能
✅ 专业界面布局

## 使用说明

1. **连接设置**
   - 选择串口端口
   - 设置波特率（默认250000）
   - 点击"连接"按钮

2. **数据监控**
   - 左侧3D图：显示Normal 1-5传感器位置和数值
   - 右侧面板：显示所有10个传感器实时数值
   - 顶部：实时时间显示

3. **数据管理**
   - 点击"开始记录"保存数据
   - 点击"导出数据"保存为CSV文件

## 数据格式

输入数据格式: 1333,638,1331,1271,1327,629,1241,631,1259,629
- 前5个数值: Normal 1-5（3D可视化）
- 后5个数值: Spear 1-4 + Test（面板显示）

## 文件说明

- SerialDataVisualizer_Professional.exe - 主程序
- 启动程序.bat - 启动脚本
- 演示模式.bat - 演示模式启动脚本
- config.ini - 配置文件
- 使用说明.md - 详细使用说明
- README_Professional.md - 专业版说明
- data/ - 数据存储目录
- exports/ - 数据导出目录
- logs/ - 日志文件目录
- docs/ - 技术文档目录

## 技术支持

开发团队: 米醋电子工作室
技术支持: Alex (工程师)

如有问题，请查看详细的"使用说明.md"文件。
