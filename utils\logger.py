#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志工具模块
版权归属: 米醋电子工作室
"""

import logging
import os
from datetime import datetime
from logging.handlers import RotatingFileHandler


def setup_logger(name: str = "SerialVisualizer", level: int = logging.INFO) -> logging.Logger:
    """设置应用程序日志器"""
    
    # 创建日志器
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器 (轮转日志)
    log_file = "serial_visualizer.log"
    file_handler = RotatingFileHandler(
        log_file, 
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    logger.info(f"日志系统初始化完成 - 日志文件: {log_file}")
    return logger


def get_logger(name: str = "SerialVisualizer") -> logging.Logger:
    """获取日志器实例"""
    return logging.getLogger(name)
