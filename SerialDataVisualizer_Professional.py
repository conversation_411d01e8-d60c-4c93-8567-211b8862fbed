#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
串口数据可视化软件 - 专业版本 (按照参考图设计)
版权归属: 米醋电子工作室
创建日期: 2025-08-26
作者: <PERSON> (工程师)

专业可视化界面，完全按照用户提供的参考图设计
"""

import sys
import os
import time
import threading
import signal
import argparse
from datetime import datetime
from typing import List, Optional, Dict, Any
import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import matplotlib.animation as animation
from mpl_toolkits.mplot3d import Axes3D
import numpy as np

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 检查串口库
try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
except ImportError:
    SERIAL_AVAILABLE = False

import csv
import configparser
import logging
from logging.handlers import RotatingFileHandler


# ==================== 常量定义 ====================
APP_NAME = "串口数据可视化软件 - 专业版"
APP_VERSION = "1.1.0"
APP_AUTHOR = "米醋电子工作室"

# 传感器位置 (与参考图一致)
SENSOR_POSITIONS = [
    (0.5, 0.5),  # Normal 1 - 中心
    (1.0, 1.0),  # Normal 2 - 右上
    (1.0, 0.0),  # Normal 3 - 右下
    (0.0, 1.0),  # Normal 4 - 左上
    (0.0, 0.0),  # Normal 5 - 左下
]

# 传感器标签 (与参考图一致)
SENSOR_LABELS = [
    "Normal 1", "Normal 2", "Normal 3", "Normal 4", "Normal 5",
    "Spear 1", "Spear 2", "Spear 3", "Spear 4", "Test"
]

# 扩展波特率支持
SERIAL_BAUDRATES = [
    1200, 2400, 4800, 9600, 14400, 19200, 28800, 38400, 
    57600, 76800, 115200, 128000, 153600, 230400, 250000, 
    256000, 460800, 500000, 576000, 921600, 1000000, 1152000, 
    1500000, 2000000, 2500000, 3000000
]

COMMON_BAUDRATES = [9600, 19200, 38400, 57600, 115200, 230400, 250000, 460800, 921600]


# ==================== 日志系统 ====================
def setup_logger(name: str = "SerialVisualizer", level: int = logging.INFO) -> logging.Logger:
    """设置应用程序日志器"""
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    if logger.handlers:
        return logger
    
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    try:
        file_handler = RotatingFileHandler(
            "serial_visualizer.log", 
            maxBytes=10*1024*1024,
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    except:
        pass
    
    return logger


# ==================== 数据结构 ====================
class SensorData:
    """传感器数据结构"""
    def __init__(self, timestamp, sensor_values, extended_data, raw_data):
        self.timestamp = timestamp
        self.sensor_values = sensor_values  # Normal 1-5
        self.extended_data = extended_data  # Spear 1-4 + Test
        self.raw_data = raw_data


# ==================== 串口管理器 ====================
class SerialManager:
    """串口通信管理器"""
    
    def __init__(self):
        self.logger = setup_logger()
        self.serial_port = None
        self.is_connected = False
        self.is_running = False
        
        # 连接参数
        self.port_name = "COM1"
        self.baudrate = 250000
        self.timeout = 1.0
        
        # 回调函数
        self.data_callback = None
        self.error_callback = None
        
        # 读取线程
        self.read_thread = None
    
    def scan_ports(self) -> List[str]:
        """扫描可用串口"""
        if not SERIAL_AVAILABLE:
            return ["COM1", "COM2", "COM3"]
        
        try:
            ports = serial.tools.list_ports.comports()
            return [port.device for port in ports]
        except Exception as e:
            self.logger.error(f"扫描串口失败: {e}")
            return []
    
    def set_connection_params(self, port: str, baudrate: int):
        """设置连接参数"""
        self.port_name = port
        self.baudrate = baudrate
    
    def set_callbacks(self, data_callback=None, error_callback=None):
        """设置回调函数"""
        self.data_callback = data_callback
        self.error_callback = error_callback
    
    def connect(self) -> bool:
        """连接串口"""
        if self.is_connected:
            return True
        
        if not SERIAL_AVAILABLE:
            self.logger.info(f"模拟连接到 {self.port_name}")
            self.is_connected = True
            self.is_running = True
            self.read_thread = threading.Thread(target=self._mock_data_loop, daemon=True)
            self.read_thread.start()
            return True
        
        try:
            self.serial_port = serial.Serial(
                port=self.port_name,
                baudrate=self.baudrate,
                bytesize=8,
                stopbits=1,
                parity='N',
                timeout=self.timeout
            )
            
            if self.serial_port.is_open:
                self.is_connected = True
                self.is_running = True
                self.read_thread = threading.Thread(target=self._read_data_loop, daemon=True)
                self.read_thread.start()
                self.logger.info(f"串口连接成功: {self.port_name}")
                return True
            else:
                raise Exception("串口打开失败")
                
        except Exception as e:
            self.logger.error(f"串口连接失败: {e}")
            if self.error_callback:
                self.error_callback(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开串口连接"""
        self.is_running = False
        
        if self.serial_port and self.serial_port.is_open:
            try:
                self.serial_port.close()
                self.logger.info("串口已断开")
            except Exception as e:
                self.logger.error(f"断开串口时出错: {e}")
        
        self.is_connected = False
        self.serial_port = None
    
    def _read_data_loop(self):
        """真实串口数据读取循环"""
        buffer = b''
        
        while self.is_running and self.serial_port and self.serial_port.is_open:
            try:
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    if data:
                        buffer += data
                        
                        while b'\n' in buffer:
                            line, buffer = buffer.split(b'\n', 1)
                            if line.strip() and self.data_callback:
                                self.data_callback(line.strip())
                
                time.sleep(0.01)
                
            except Exception as e:
                if self.is_running and self.error_callback:
                    self.error_callback(f"读取数据错误: {e}")
                break
    
    def _mock_data_loop(self):
        """模拟数据生成循环 - 生成与参考图类似的数据"""
        import random
        
        while self.is_running:
            try:
                # 生成与参考图类似的模拟数据
                # Normal 1-5: 1000-2000范围
                normal_values = [
                    random.randint(1200, 1800),  # Normal 1
                    random.randint(1400, 2000),  # Normal 2  
                    random.randint(1100, 1700),  # Normal 3
                    random.randint(1300, 1900),  # Normal 4
                    random.randint(1200, 1800),  # Normal 5
                ]
                
                # Spear 1-4 + Test: 600-1300范围
                extended_values = [
                    random.randint(600, 900),    # Spear 1
                    random.randint(800, 1300),   # Spear 2
                    random.randint(600, 800),    # Spear 3
                    random.randint(900, 1300),   # Spear 4
                    random.randint(600, 700),    # Test
                ]
                
                all_values = normal_values + extended_values
                mock_data = ','.join(map(str, all_values))
                
                if self.data_callback:
                    self.data_callback(mock_data.encode())
                
                time.sleep(0.1)  # 10Hz数据率
                
            except Exception as e:
                if self.is_running and self.error_callback:
                    self.error_callback(f"模拟数据错误: {e}")
                break


# ==================== 数据解析器 ====================
class DataParser:
    """数据解析器"""
    
    def __init__(self):
        self.logger = setup_logger()
        self.total_packets = 0
        self.valid_packets = 0
        self.error_packets = 0
    
    def parse_data(self, raw_data: bytes) -> Optional[SensorData]:
        """解析原始数据"""
        self.total_packets += 1
        
        try:
            data_str = raw_data.decode('utf-8', errors='ignore').strip()
            if not data_str:
                return None
            
            # 尝试不同的分隔符
            values = None
            for delimiter in [',', ' ', ';']:
                if delimiter in data_str:
                    parts = data_str.split(delimiter)
                    break
            else:
                parts = data_str.split()
            
            # 过滤空字符串并转换为浮点数
            parts = [part.strip() for part in parts if part.strip()]
            
            if len(parts) >= 10:
                values = [float(part) for part in parts[:10]]
            elif len(parts) >= 5:
                values = [float(part) for part in parts[:5]]
                values.extend([0.0] * (10 - len(values)))
            else:
                raise ValueError(f"数据包大小不足: {len(parts)}")
            
            # 分离传感器数据 (Normal 1-5) 和扩展数据 (Spear 1-4 + Test)
            sensor_values = values[:5]  # Normal 1-5
            extended_data = values[5:10]  # Spear 1-4 + Test
            
            sensor_data = SensorData(
                timestamp=datetime.now(),
                sensor_values=sensor_values,
                extended_data=extended_data,
                raw_data=data_str
            )
            
            self.valid_packets += 1
            return sensor_data
            
        except Exception as e:
            self.error_packets += 1
            self.logger.error(f"数据解析失败: {e}")
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取解析统计信息"""
        if self.total_packets > 0:
            success_rate = (self.valid_packets / self.total_packets) * 100
        else:
            success_rate = 0
        
        return {
            'total_packets': self.total_packets,
            'valid_packets': self.valid_packets,
            'error_packets': self.error_packets,
            'success_rate': round(success_rate, 2)
        }


# ==================== 专业可视化界面 ====================
class ProfessionalVisualizerGUI:
    """专业可视化界面 - 按照参考图设计"""

    def __init__(self):
        self.logger = setup_logger()

        # 初始化组件
        self.serial_manager = SerialManager()
        self.data_parser = DataParser()

        # 数据
        self.current_data = None
        self.data_history = []
        self.is_recording = False

        # 设置回调
        self.serial_manager.set_callbacks(
            data_callback=self._on_data_received,
            error_callback=self._on_error
        )

        # 创建GUI
        self.setup_gui()

        # 启动数据更新
        self.update_display()

    def setup_gui(self):
        """设置GUI界面"""
        # 创建主窗口
        self.root = tk.Tk()
        self.root.title(f"{APP_NAME} v{APP_VERSION}")
        self.root.geometry("1200x800")
        self.root.configure(bg='white')

        # 设置窗口图标和属性
        self.root.resizable(True, True)

        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建标题
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        title_label = ttk.Label(
            title_frame,
            text="Sensor Readings",
            font=('Arial', 16, 'bold')
        )
        title_label.pack()

        # 创建内容框架
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧：3D可视化
        self.setup_3d_plot(content_frame)

        # 右侧：传感器数据显示
        self.setup_sensor_panel(content_frame)

        # 底部：控制面板
        self.setup_control_panel(main_frame)

        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def setup_3d_plot(self, parent):
        """设置3D绘图区域"""
        # 左侧框架
        left_frame = ttk.Frame(parent)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 时间标签
        time_frame = ttk.Frame(left_frame)
        time_frame.pack(fill=tk.X, pady=(0, 10))

        self.time_label = ttk.Label(
            time_frame,
            text="Real-time Sensor Data - 00:00:00",
            font=('Arial', 12, 'bold')
        )
        self.time_label.pack(anchor=tk.W)

        # 创建matplotlib图形
        self.fig = Figure(figsize=(8, 6), dpi=100, facecolor='white')
        self.ax = self.fig.add_subplot(111, projection='3d')

        # 设置3D图形样式 (与参考图一致)
        self.ax.set_xlim(0, 1)
        self.ax.set_ylim(0, 1)
        self.ax.set_zlim(0, 4000)

        self.ax.set_xlabel('X Position', fontsize=10)
        self.ax.set_ylabel('Y Position', fontsize=10)
        self.ax.set_zlabel('Value', fontsize=10)

        # 设置网格和背景
        self.ax.grid(True, alpha=0.3)
        self.ax.xaxis.pane.fill = False
        self.ax.yaxis.pane.fill = False
        self.ax.zaxis.pane.fill = False

        # 设置网格颜色
        self.ax.xaxis.pane.set_edgecolor('gray')
        self.ax.yaxis.pane.set_edgecolor('gray')
        self.ax.zaxis.pane.set_edgecolor('gray')
        self.ax.xaxis.pane.set_alpha(0.1)
        self.ax.yaxis.pane.set_alpha(0.1)
        self.ax.zaxis.pane.set_alpha(0.1)

        # 初始化散点图
        self.scatter = None

        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.fig, left_frame)
        self.canvas.draw()
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def setup_sensor_panel(self, parent):
        """设置传感器数据面板"""
        # 右侧框架
        right_frame = ttk.Frame(parent)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_frame.configure(width=300)

        # 传感器数据标签
        self.sensor_labels = {}

        # 创建所有传感器标签 (与参考图一致)
        for i, label_text in enumerate(SENSOR_LABELS):
            # 创建框架
            sensor_frame = ttk.Frame(right_frame)
            sensor_frame.pack(fill=tk.X, pady=2)

            # 根据传感器类型设置颜色
            if label_text.startswith("Normal"):
                bg_color = "#E3F2FD"  # 浅蓝色
                text_color = "#1976D2"
            elif label_text.startswith("Spear"):
                bg_color = "#E8F5E8"  # 浅绿色
                text_color = "#388E3C"
            else:  # Test
                bg_color = "#FFF3E0"  # 浅橙色
                text_color = "#F57C00"

            # 创建标签
            label = tk.Label(
                sensor_frame,
                text=f"{label_text}: 0.00",
                font=('Arial', 11, 'bold'),
                bg=bg_color,
                fg=text_color,
                relief=tk.RAISED,
                bd=1,
                padx=10,
                pady=5
            )
            label.pack(fill=tk.X)

            self.sensor_labels[label_text] = label

    def setup_control_panel(self, parent):
        """设置控制面板"""
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, pady=(10, 0))

        # 连接控制
        conn_frame = ttk.LabelFrame(control_frame, text="连接控制")
        conn_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        # 端口选择
        ttk.Label(conn_frame, text="端口:").grid(row=0, column=0, padx=5, pady=5)
        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(conn_frame, textvariable=self.port_var, width=10)
        self.port_combo.grid(row=0, column=1, padx=5, pady=5)

        # 波特率选择
        ttk.Label(conn_frame, text="波特率:").grid(row=0, column=2, padx=5, pady=5)
        self.baud_var = tk.StringVar(value="250000")
        self.baud_combo = ttk.Combobox(conn_frame, textvariable=self.baud_var, width=10)
        self.baud_combo['values'] = [str(rate) for rate in COMMON_BAUDRATES]
        self.baud_combo.grid(row=0, column=3, padx=5, pady=5)

        # 连接按钮
        self.connect_btn = ttk.Button(conn_frame, text="连接", command=self.toggle_connection)
        self.connect_btn.grid(row=0, column=4, padx=5, pady=5)

        # 状态显示
        self.status_var = tk.StringVar(value="未连接")
        status_label = ttk.Label(conn_frame, textvariable=self.status_var, foreground="red")
        status_label.grid(row=0, column=5, padx=5, pady=5)

        # 数据控制
        data_frame = ttk.LabelFrame(control_frame, text="数据控制")
        data_frame.pack(side=tk.RIGHT, padx=(5, 0))

        self.record_btn = ttk.Button(data_frame, text="开始记录", command=self.toggle_recording)
        self.record_btn.pack(side=tk.LEFT, padx=5, pady=5)

        export_btn = ttk.Button(data_frame, text="导出数据", command=self.export_data)
        export_btn.pack(side=tk.LEFT, padx=5, pady=5)

        # 扫描端口
        self.scan_ports()

    def scan_ports(self):
        """扫描可用端口"""
        ports = self.serial_manager.scan_ports()
        self.port_combo['values'] = ports
        if ports:
            self.port_var.set(ports[0])

    def toggle_connection(self):
        """切换连接状态"""
        if not self.serial_manager.is_connected:
            port = self.port_var.get()
            baudrate = int(self.baud_var.get())

            self.serial_manager.set_connection_params(port, baudrate)
            if self.serial_manager.connect():
                self.connect_btn.config(text="断开")
                self.status_var.set("已连接")
                self.status_var.set("已连接")
                # 更新状态标签颜色
                for widget in self.root.winfo_children():
                    if isinstance(widget, ttk.Frame):
                        for child in widget.winfo_children():
                            if isinstance(child, ttk.Frame):
                                for grandchild in child.winfo_children():
                                    if isinstance(grandchild, ttk.Label) and grandchild.cget("textvariable") == str(self.status_var):
                                        grandchild.config(foreground="green")
            else:
                messagebox.showerror("连接错误", "无法连接到串口")
        else:
            self.serial_manager.disconnect()
            self.connect_btn.config(text="连接")
            self.status_var.set("未连接")

    def toggle_recording(self):
        """切换记录状态"""
        self.is_recording = not self.is_recording
        if self.is_recording:
            self.data_history.clear()
            self.record_btn.config(text="停止记录")
        else:
            self.record_btn.config(text="开始记录")

    def export_data(self):
        """导出数据"""
        if not self.data_history:
            messagebox.showwarning("导出警告", "没有数据可导出")
            return

        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    fieldnames = ['timestamp'] + SENSOR_LABELS + ['raw_data']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()

                    for data in self.data_history:
                        row = {'timestamp': data.timestamp.isoformat()}

                        # Normal 1-5
                        for i, value in enumerate(data.sensor_values):
                            row[SENSOR_LABELS[i]] = value

                        # Spear 1-4 + Test
                        for i, value in enumerate(data.extended_data):
                            row[SENSOR_LABELS[i + 5]] = value

                        row['raw_data'] = data.raw_data
                        writer.writerow(row)

                messagebox.showinfo("导出成功", f"数据已导出到: {filename}")

        except Exception as e:
            messagebox.showerror("导出错误", f"导出失败: {e}")

    def _on_data_received(self, raw_data: bytes):
        """数据接收回调"""
        sensor_data = self.data_parser.parse_data(raw_data)
        if sensor_data:
            self.current_data = sensor_data
            if self.is_recording:
                self.data_history.append(sensor_data)

    def _on_error(self, error_msg: str):
        """错误回调"""
        self.logger.error(error_msg)

    def update_display(self):
        """更新显示"""
        try:
            # 更新时间
            current_time = datetime.now().strftime("%H:%M:%S")
            self.time_label.config(text=f"Real-time Sensor Data - {current_time}")

            # 更新传感器数据显示
            if self.current_data:
                # 更新Normal 1-5
                for i, value in enumerate(self.current_data.sensor_values):
                    label_text = SENSOR_LABELS[i]
                    self.sensor_labels[label_text].config(text=f"{label_text}: {value:.2f}")

                # 更新Spear 1-4 + Test
                for i, value in enumerate(self.current_data.extended_data):
                    label_text = SENSOR_LABELS[i + 5]
                    self.sensor_labels[label_text].config(text=f"{label_text}: {value:.2f}")

                # 更新3D图形
                self.update_3d_plot()

        except Exception as e:
            self.logger.error(f"更新显示时出错: {e}")

        # 定时更新
        self.root.after(100, self.update_display)

    def update_3d_plot(self):
        """更新3D图形"""
        if not self.current_data:
            return

        try:
            # 清除之前的散点图
            if self.scatter:
                self.scatter.remove()

            # 准备数据
            x_coords = [pos[0] for pos in SENSOR_POSITIONS]
            y_coords = [pos[1] for pos in SENSOR_POSITIONS]
            z_coords = self.current_data.sensor_values

            # 根据数值设置颜色 (与参考图类似)
            colors = []
            for value in z_coords:
                if value < 1000:
                    colors.append('blue')
                elif value < 1500:
                    colors.append('green')
                elif value < 2000:
                    colors.append('orange')
                else:
                    colors.append('red')

            # 绘制散点图
            self.scatter = self.ax.scatter(
                x_coords, y_coords, z_coords,
                c=colors, s=100, alpha=0.8, edgecolors='black'
            )

            # 添加数值标签
            for i, (x, y, z) in enumerate(zip(x_coords, y_coords, z_coords)):
                self.ax.text(x, y, z + 100, f'{z:.0f}', fontsize=8)

            # 刷新画布
            self.canvas.draw_idle()

        except Exception as e:
            self.logger.error(f"更新3D图形时出错: {e}")

    def on_closing(self):
        """窗口关闭事件"""
        if self.serial_manager.is_connected:
            self.serial_manager.disconnect()
        self.root.destroy()

    def run(self):
        """运行应用程序"""
        self.root.mainloop()


# ==================== 主函数 ====================
def main():
    """主函数"""
    parser = argparse.ArgumentParser(description=f'{APP_NAME} v{APP_VERSION}')
    parser.add_argument('--port', '-p', type=str, help='串口端口 (如: COM1)')
    parser.add_argument('--baudrate', '-b', type=int, default=250000, help='波特率 (默认: 250000)')

    args = parser.parse_args()

    try:
        app = ProfessionalVisualizerGUI()

        # 如果指定了端口参数，自动设置
        if args.port:
            app.port_var.set(args.port)
        if args.baudrate:
            app.baud_var.set(str(args.baudrate))

        app.run()

    except Exception as e:
        print(f"应用程序启动失败: {e}")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
