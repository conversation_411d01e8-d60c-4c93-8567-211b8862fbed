#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理工具模块
版权归属: 米醋电子工作室
"""

import os
import csv
import json
from datetime import datetime
from typing import List, Dict, Any
from utils.logger import get_logger


class DataFileHandler:
    """数据文件处理器"""
    
    def __init__(self):
        self.logger = get_logger()
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        directories = ['data', 'exports', 'logs']
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.logger.info(f"创建目录: {directory}")
    
    def save_to_csv(self, data: List[Dict[str, Any]], filename: str = None) -> str:
        """保存数据到CSV文件"""
        if not data:
            raise ValueError("没有数据可保存")
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"sensor_data_{timestamp}.csv"
        
        filepath = os.path.join('exports', filename)
        
        try:
            with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                writer.writerows(data)
            
            self.logger.info(f"数据已保存到CSV文件: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存CSV文件失败: {e}")
            raise
    
    def load_from_csv(self, filepath: str) -> List[Dict[str, Any]]:
        """从CSV文件加载数据"""
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"文件不存在: {filepath}")
        
        try:
            data = []
            with open(filepath, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    # 转换数值类型
                    processed_row = {}
                    for key, value in row.items():
                        try:
                            # 尝试转换为浮点数
                            processed_row[key] = float(value)
                        except ValueError:
                            # 保持字符串类型
                            processed_row[key] = value
                    data.append(processed_row)
            
            self.logger.info(f"从CSV文件加载了 {len(data)} 条数据: {filepath}")
            return data
            
        except Exception as e:
            self.logger.error(f"加载CSV文件失败: {e}")
            raise
    
    def save_config(self, config: Dict[str, Any], filename: str = "user_config.json") -> str:
        """保存配置到JSON文件"""
        filepath = os.path.join('data', filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as jsonfile:
                json.dump(config, jsonfile, indent=2, ensure_ascii=False)
            
            self.logger.info(f"配置已保存: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            raise
    
    def load_config(self, filename: str = "user_config.json") -> Dict[str, Any]:
        """从JSON文件加载配置"""
        filepath = os.path.join('data', filename)
        
        if not os.path.exists(filepath):
            return {}
        
        try:
            with open(filepath, 'r', encoding='utf-8') as jsonfile:
                config = json.load(jsonfile)
            
            self.logger.info(f"配置已加载: {filepath}")
            return config
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def get_export_files(self) -> List[str]:
        """获取导出文件列表"""
        export_dir = 'exports'
        if not os.path.exists(export_dir):
            return []
        
        files = []
        for filename in os.listdir(export_dir):
            if filename.endswith('.csv'):
                filepath = os.path.join(export_dir, filename)
                files.append(filepath)
        
        # 按修改时间排序
        files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        return files
