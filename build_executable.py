#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建可执行文件脚本
版权归属: 米醋电子工作室
"""

import os
import sys
import shutil
import subprocess
from datetime import datetime


def check_dependencies():
    """检查依赖"""
    print("检查依赖...")
    
    required_packages = ['pyserial', 'numpy', 'configparser']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"✗ {package} (缺失)")
    
    if missing_packages:
        print(f"\n需要安装缺失的包: {', '.join(missing_packages)}")
        install = input("是否现在安装? (y/n): ").lower().strip()
        if install == 'y':
            for package in missing_packages:
                subprocess.run([sys.executable, '-m', 'pip', 'install', package])
        else:
            print("请手动安装缺失的依赖包")
            return False
    
    return True


def create_spec_file():
    """创建PyInstaller规格文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['serial_visualizer_cli.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('utils', 'utils'),
        ('core', 'core'),
    ],
    hiddenimports=[
        'serial',
        'serial.tools.list_ports',
        'numpy',
        'configparser',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='SerialDataVisualizer',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)
'''
    
    with open('serial_visualizer.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✓ PyInstaller规格文件已创建")


def build_executable():
    """构建可执行文件"""
    print("\n开始构建可执行文件...")
    
    try:
        # 检查PyInstaller
        try:
            import PyInstaller
            print("✓ PyInstaller 已安装")
        except ImportError:
            print("安装 PyInstaller...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        
        # 创建规格文件
        create_spec_file()
        
        # 运行PyInstaller
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'serial_visualizer.spec']
        print(f"执行命令: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 可执行文件构建成功!")
            
            # 检查输出文件
            exe_path = os.path.join('dist', 'SerialDataVisualizer.exe')
            if os.path.exists(exe_path):
                size_mb = os.path.getsize(exe_path) / (1024 * 1024)
                print(f"✓ 可执行文件: {exe_path} ({size_mb:.1f} MB)")
                return exe_path
            else:
                print("✗ 可执行文件未找到")
                return None
        else:
            print("✗ 构建失败:")
            print(result.stderr)
            return None
            
    except Exception as e:
        print(f"✗ 构建过程出错: {e}")
        return None


def create_portable_package(exe_path):
    """创建便携式软件包"""
    if not exe_path or not os.path.exists(exe_path):
        return None
    
    print("\n创建便携式软件包...")
    
    # 创建软件包目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"SerialDataVisualizer_v1.0_{timestamp}"
    package_dir = os.path.join('release', package_name)
    
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    os.makedirs(package_dir, exist_ok=True)
    
    try:
        # 复制可执行文件
        shutil.copy2(exe_path, package_dir)
        print(f"✓ 复制可执行文件到: {package_dir}")
        
        # 复制演示脚本
        demo_files = ['demo.py', 'test_basic.py']
        for demo_file in demo_files:
            if os.path.exists(demo_file):
                shutil.copy2(demo_file, package_dir)
        
        # 创建README文件
        readme_content = f"""# 串口数据可视化软件 v1.0

## 软件信息
- 版本: 1.0.0
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 版权归属: 米醋电子工作室

## 使用说明

### 1. 运行主程序
双击 `SerialDataVisualizer.exe` 或在命令行中运行:
```
SerialDataVisualizer.exe --help
```

### 2. 命令行参数
- `--port, -p`: 指定串口端口 (如: COM1, COM2)
- `--baudrate, -b`: 指定波特率 (默认: 115200)

### 3. 使用示例
```
# 使用默认设置 (自动检测端口)
SerialDataVisualizer.exe

# 指定端口和波特率
SerialDataVisualizer.exe --port COM3 --baudrate 9600

# 运行演示模式
python demo.py
```

### 4. 数据格式
单片机发送的数据格式应为:
```
sensor1,sensor2,sensor3,sensor4,sensor5,ext1,ext2,ext3,ext4,ext5
```
或使用空格、分号分隔。

### 5. 传感器位置
- 传感器1: 中心位置 (0.5, 0.5)
- 传感器2: 右上角 (1.0, 1.0)
- 传感器3: 右下角 (1.0, 0.0)
- 传感器4: 左上角 (0.0, 1.0)
- 传感器5: 左下角 (0.0, 0.0)

### 6. 功能特点
- 实时串口数据接收
- ASCII艺术3D可视化
- 数据记录和导出
- 自动端口检测
- 模拟数据模式

### 7. 输出文件
- 数据导出: `exports/` 目录下的CSV文件
- 日志文件: `serial_visualizer.log`
- 配置文件: `config.ini`

### 8. 系统要求
- Windows 10/11 64位
- 无需安装Python环境
- 支持便携式运行

### 9. 技术支持
如有问题请联系: 米醋电子工作室

---
构建信息: {package_name}
"""
        
        with open(os.path.join(package_dir, 'README.md'), 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✓ README文件已创建")
        
        # 创建启动脚本
        batch_content = f"""@echo off
title 串口数据可视化软件
echo 启动串口数据可视化软件...
echo 版权归属: 米醋电子工作室
echo.
SerialDataVisualizer.exe
pause
"""
        
        with open(os.path.join(package_dir, '启动程序.bat'), 'w', encoding='gbk') as f:
            f.write(batch_content)
        
        print("✓ 启动脚本已创建")
        
        # 创建压缩包
        try:
            shutil.make_archive(package_dir, 'zip', 'release', package_name)
            zip_path = f"{package_dir}.zip"
            zip_size_mb = os.path.getsize(zip_path) / (1024 * 1024)
            print(f"✓ 压缩包已创建: {zip_path} ({zip_size_mb:.1f} MB)")
        except Exception as e:
            print(f"创建压缩包失败: {e}")
        
        return package_dir
        
    except Exception as e:
        print(f"✗ 创建软件包失败: {e}")
        return None


def main():
    """主函数"""
    print("=" * 60)
    print("           串口数据可视化软件 - 构建脚本")
    print("              版权归属: 米醋电子工作室")
    print("=" * 60)
    print()
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 构建可执行文件
    exe_path = build_executable()
    if not exe_path:
        print("\n构建失败!")
        return 1
    
    # 创建便携式软件包
    package_dir = create_portable_package(exe_path)
    if package_dir:
        print(f"\n🎉 构建完成!")
        print(f"软件包位置: {package_dir}")
        print(f"可执行文件: {exe_path}")
        print("\n使用说明:")
        print("1. 解压软件包到任意目录")
        print("2. 双击 '启动程序.bat' 或直接运行 'SerialDataVisualizer.exe'")
        print("3. 查看 README.md 了解详细使用方法")
    else:
        print("\n软件包创建失败，但可执行文件已生成")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
