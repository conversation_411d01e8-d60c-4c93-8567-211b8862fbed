# 串口数据可视化软件 - 详细使用说明

**版权归属**: 米醋电子工作室  
**版本**: v1.1.0 Professional  
**更新日期**: 2025-08-26

## 🎯 软件简介

这是一个专业的串口数据可视化软件，完全按照用户参考图设计，提供实时数据监控和3D可视化功能。

## 🚀 三种使用方式

### 方式一：直接运行exe文件（最简单）

1. **下载软件包**
   - 找到 `SerialDataVisualizer_Release_XXXXXX` 文件夹
   - 这是已经打包好的可执行版本

2. **启动软件**
   ```
   双击 "启动程序.bat" 
   或者
   双击 "SerialDataVisualizer.exe"
   ```

3. **演示模式**（无需串口设备）
   ```
   双击 "演示模式.bat"
   ```

### 方式二：Python环境运行专业版（推荐）

1. **检查Python环境**
   ```bash
   python --version  # 需要Python 3.7+
   ```

2. **安装依赖库**
   ```bash
   pip install matplotlib numpy tkinter
   # 可选：pip install pyserial  # 用于真实串口通信
   ```

3. **运行专业版软件**
   ```bash
   # 方式1：使用启动器（推荐）
   python run_professional_visualizer.py
   
   # 方式2：直接运行
   python SerialDataVisualizer_Professional.py
   
   # 方式3：指定参数
   python SerialDataVisualizer_Professional.py --port COM3 --baudrate 250000
   ```

### 方式三：Python环境运行标准版

```bash
# GUI版本
python SerialDataVisualizer.py

# 命令行版本
python serial_visualizer_cli.py --port COM1 --baudrate 9600

# 演示模式
python demo.py
```

## 📊 专业版界面说明（按照参考图设计）

### 界面布局
```
┌─────────────────────────────────────────────────────────┐
│                    Sensor Readings                     │
├─────────────────────────┬───────────────────────────────┤
│                         │  Normal 1: 1333.00           │
│     3D坐标图             │  Normal 2: 638.00            │
│   (显示前5个传感器)        │  Normal 3: 1331.00           │
│                         │  Normal 4: 1271.00           │
│  Real-time Sensor Data  │  Normal 5: 1327.00           │
│      - 15:30:45         │                               │
│                         │  Spear 1: 629.00             │
│                         │  Spear 2: 1241.00            │
│                         │  Spear 3: 631.00             │
│                         │  Spear 4: 1259.00            │
│                         │                               │
│                         │  Test: 629.00                │
├─────────────────────────┴───────────────────────────────┤
│ 端口: COM3  波特率: 250000  [连接] 状态: 已连接          │
│ [开始记录] [导出数据]                                   │
└─────────────────────────────────────────────────────────┘
```

### 功能区域说明

1. **左侧3D坐标图**
   - 显示前5个传感器（Normal 1-5）的位置和数值
   - 传感器位置：(0.5,0.5), (1,1), (1,0), (0,1), (0,0)
   - 颜色编码：蓝色(<1000), 绿色(1000-1500), 橙色(1500-2000), 红色(>2000)

2. **右侧传感器面板**
   - Normal 1-5：浅蓝色背景
   - Spear 1-4：浅绿色背景
   - Test：浅橙色背景

3. **底部控制面板**
   - 串口连接设置
   - 数据记录和导出功能

## 🔌 连接设置

### 1. 选择串口
- 软件会自动扫描可用串口
- 从下拉菜单选择正确的端口（如：COM1, COM3等）

### 2. 设置波特率
- 专业版默认：250000
- 标准版默认：115200
- 支持范围：1200 - 3000000

### 3. 建立连接
- 点击"连接"按钮
- 状态显示"已连接"表示成功
- 开始接收和显示数据

## 📡 数据格式要求

### 输入数据格式
```
1333,638,1331,1271,1327,629,1241,631,1259,629
```

### 数据说明
- **前5个数值**：Normal 1-5（用于3D可视化）
- **后5个数值**：Spear 1-4 + Test（右侧面板显示）
- **分隔符**：支持逗号、空格、分号

### 数据范围（模拟数据）
- Normal 1-5：1000-2000
- Spear 1-4：600-1300  
- Test：600-700

## 🎮 操作指南

### 基本操作流程

1. **启动软件**
   ```bash
   python run_professional_visualizer.py
   ```

2. **连接设备**
   - 选择串口端口
   - 设置波特率
   - 点击"连接"

3. **查看数据**
   - 左侧3D图：观察传感器位置和数值变化
   - 右侧面板：查看所有传感器实时数值
   - 顶部时间：确认数据实时更新

4. **记录数据**
   - 点击"开始记录"开始保存数据
   - 点击"停止记录"结束记录
   - 点击"导出数据"保存为CSV文件

### 3D视图操作
- **旋转视图**：鼠标左键拖拽
- **缩放视图**：鼠标滚轮
- **重置视图**：关闭重新打开软件

## 📁 文件说明

### 核心文件
- `SerialDataVisualizer_Professional.py` - 专业版主程序
- `run_professional_visualizer.py` - 启动器
- `SerialDataVisualizer.py` - 标准版主程序
- `requirements.txt` - 依赖库列表

### 配置文件
- `config.ini` - 软件配置
- `serial_visualizer.log` - 运行日志

### 数据文件
- `exports/` - 导出的数据文件
- `data/` - 数据存储目录

## 🔧 故障排除

### 常见问题

1. **软件无法启动**
   ```bash
   # 检查Python环境
   python --version
   
   # 安装依赖
   pip install matplotlib numpy tkinter
   
   # 使用启动器检查
   python run_professional_visualizer.py
   ```

2. **串口连接失败**
   - 检查串口是否被其他程序占用
   - 确认设备管理器中串口状态
   - 尝试不同的波特率设置
   - 重新插拔串口设备

3. **数据显示异常**
   - 检查数据格式是否正确（10个数值）
   - 确认数据分隔符（逗号、空格、分号）
   - 查看控制台错误信息
   - 检查波特率是否匹配

4. **界面显示问题**
   - 确保matplotlib库正确安装
   - 检查系统图形驱动
   - 尝试重启软件

### 测试功能
```bash
# 运行自动化测试
python test_professional_visualizer.py

# 运行基础测试
python test_basic.py
```

## 📞 技术支持

**开发团队**：米醋电子工作室  
**技术支持**：Alex (工程师)  

### 获取帮助
1. 查看日志文件：`serial_visualizer.log`
2. 运行测试脚本检查问题
3. 联系技术支持团队

---

## 🎯 快速开始检查清单

- [ ] 确认Python 3.7+环境
- [ ] 安装必要依赖库
- [ ] 选择合适的启动方式
- [ ] 配置串口参数
- [ ] 测试数据接收
- [ ] 验证界面显示

**恭喜！您现在可以开始使用专业的串口数据可视化软件了！** 🎉
