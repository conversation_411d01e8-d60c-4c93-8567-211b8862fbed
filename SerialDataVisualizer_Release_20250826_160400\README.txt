# 串口数据可视化软件 v1.0

## 使用方法

### 1. 快速开始
双击 `SerialDataVisualizer.exe` 启动程序

### 2. 命令行使用
```
SerialDataVisualizer.exe --help          # 查看帮助
SerialDataVisualizer.exe --demo          # 运行演示模式
SerialDataVisualizer.exe --port COM3     # 指定串口
```

### 3. 数据格式
单片机发送数据格式 (10个数值):
```
1000,1500,2000,2500,3000,100,200,300,400,500
```
- 前5个: 传感器数据 (用于可视化)
- 后5个: 扩展数据

### 4. 传感器位置
- 传感器1: 中心 (0.5, 0.5)
- 传感器2: 右上 (1.0, 1.0)
- 传感器3: 右下 (1.0, 0.0)
- 传感器4: 左上 (0.0, 1.0)
- 传感器5: 左下 (0.0, 0.0)

### 5. 操作说明
- 程序启动后会自动扫描可用串口
- 选择正确的端口和波特率
- 按 'r' 开始/停止记录数据
- 按 'e' 导出数据到CSV文件
- 按 'q' 退出程序

### 6. 输出文件
- 数据导出: exports/ 目录下的CSV文件
- 日志文件: serial_visualizer.log
- 配置文件: config.ini

### 7. 系统要求
- Windows 10/11 64位
- 无需安装Python环境
- 支持便携式运行

---
版权归属: 米醋电子工作室
构建时间: 2025-08-26 16:04:00
