#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
控制面板组件
版权归属: 米醋电子工作室
"""

from datetime import datetime
from typing import List, Dict, Any
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                            QLabel, QComboBox, QPushButton, QLineEdit, 
                            QCheckBox, QTextEdit, QProgressBar, QSpinBox,
                            QFrame, QGridLayout, QScrollArea)
from PyQt5.QtCore import pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPalette, QColor
from core.data_parser import SensorData
from config.constants import SERIAL_BAUDRATES, UI_SIZES
from utils.logger import get_logger


class ControlPanel(QWidget):
    """右侧控制面板"""
    
    # 信号定义
    connect_requested = pyqtSignal(str, int)  # 连接请求信号
    disconnect_requested = pyqtSignal()  # 断开连接信号
    recording_toggled = pyqtSignal(bool)  # 记录开关信号
    export_requested = pyqtSignal()  # 导出请求信号
    clear_data_requested = pyqtSignal()  # 清空数据信号
    pause_toggled = pyqtSignal(bool)  # 暂停开关信号
    view_reset_requested = pyqtSignal()  # 重置视图信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger()
        
        # 状态变量
        self.is_connected = False
        self.is_recording = False
        self.is_paused = False
        self.current_data: SensorData = None
        
        # 统计信息
        self.total_packets = 0
        self.valid_packets = 0
        self.start_time = None
        
        # 初始化UI
        self.init_ui()
        
        # 更新定时器
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        self.update_timer.start(100)  # 100ms更新一次
        
        self.logger.info("控制面板初始化完成")
    
    def init_ui(self):
        """初始化用户界面"""
        self.setFixedWidth(UI_SIZES['CONTROL_PANEL_WIDTH'])
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(10)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 串口连接组
        self.create_connection_group(scroll_layout)
        
        # 数据显示组
        self.create_data_display_group(scroll_layout)
        
        # 控制按钮组
        self.create_control_group(scroll_layout)
        
        # 统计信息组
        self.create_statistics_group(scroll_layout)
        
        # 状态日志组
        self.create_log_group(scroll_layout)
        
        # 设置滚动区域
        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        main_layout.addWidget(scroll_area)
        
        self.setLayout(main_layout)
    
    def create_connection_group(self, parent_layout):
        """创建连接控制组"""
        group = QGroupBox("串口连接")
        layout = QVBoxLayout()
        
        # 端口选择
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("端口:"))
        self.port_combo = QComboBox()
        self.port_combo.setEditable(True)
        self.port_combo.addItems(["COM1", "COM2", "COM3", "COM4", "COM5"])
        port_layout.addWidget(self.port_combo)
        layout.addLayout(port_layout)
        
        # 波特率选择
        baud_layout = QHBoxLayout()
        baud_layout.addWidget(QLabel("波特率:"))
        self.baud_combo = QComboBox()
        self.baud_combo.addItems([str(rate) for rate in SERIAL_BAUDRATES])
        self.baud_combo.setCurrentText("115200")
        baud_layout.addWidget(self.baud_combo)
        layout.addLayout(baud_layout)
        
        # 连接按钮
        button_layout = QHBoxLayout()
        self.connect_btn = QPushButton("连接")
        self.connect_btn.clicked.connect(self.on_connect_clicked)
        self.disconnect_btn = QPushButton("断开")
        self.disconnect_btn.clicked.connect(self.on_disconnect_clicked)
        self.disconnect_btn.setEnabled(False)
        
        button_layout.addWidget(self.connect_btn)
        button_layout.addWidget(self.disconnect_btn)
        layout.addLayout(button_layout)
        
        # 连接状态指示
        self.connection_status = QLabel("未连接")
        self.connection_status.setStyleSheet("color: red; font-weight: bold;")
        layout.addWidget(self.connection_status)
        
        group.setLayout(layout)
        parent_layout.addWidget(group)
    
    def create_data_display_group(self, parent_layout):
        """创建数据显示组"""
        group = QGroupBox("实时数据")
        layout = QVBoxLayout()
        
        # 时间戳显示
        self.timestamp_label = QLabel("时间: --:--:--")
        self.timestamp_label.setFont(QFont("Consolas", 9))
        layout.addWidget(self.timestamp_label)
        
        # 传感器数据显示
        sensor_layout = QGridLayout()
        self.sensor_labels = []
        self.sensor_values = []
        
        for i in range(5):
            # 传感器标签
            label = QLabel(f"传感器{i+1}:")
            sensor_layout.addWidget(label, i, 0)
            self.sensor_labels.append(label)
            
            # 数值显示
            value_label = QLabel("0.00")
            value_label.setFont(QFont("Consolas", 10, QFont.Bold))
            value_label.setStyleSheet("background-color: #f0f0f0; padding: 2px; border: 1px solid #ccc;")
            sensor_layout.addWidget(value_label, i, 1)
            self.sensor_values.append(value_label)
        
        layout.addLayout(sensor_layout)
        
        # 数据范围设置
        range_layout = QHBoxLayout()
        range_layout.addWidget(QLabel("范围:"))
        self.min_range_spin = QSpinBox()
        self.min_range_spin.setRange(0, 10000)
        self.min_range_spin.setValue(0)
        self.max_range_spin = QSpinBox()
        self.max_range_spin.setRange(0, 10000)
        self.max_range_spin.setValue(4000)
        
        range_layout.addWidget(self.min_range_spin)
        range_layout.addWidget(QLabel("-"))
        range_layout.addWidget(self.max_range_spin)
        layout.addLayout(range_layout)
        
        group.setLayout(layout)
        parent_layout.addWidget(group)
    
    def create_control_group(self, parent_layout):
        """创建控制按钮组"""
        group = QGroupBox("数据控制")
        layout = QVBoxLayout()
        
        # 第一行按钮
        row1_layout = QHBoxLayout()
        self.pause_btn = QPushButton("暂停")
        self.pause_btn.clicked.connect(self.on_pause_clicked)
        self.clear_btn = QPushButton("清空")
        self.clear_btn.clicked.connect(self.on_clear_clicked)
        
        row1_layout.addWidget(self.pause_btn)
        row1_layout.addWidget(self.clear_btn)
        layout.addLayout(row1_layout)
        
        # 第二行按钮
        row2_layout = QHBoxLayout()
        self.record_btn = QPushButton("开始记录")
        self.record_btn.clicked.connect(self.on_record_clicked)
        self.export_btn = QPushButton("导出数据")
        self.export_btn.clicked.connect(self.on_export_clicked)
        
        row2_layout.addWidget(self.record_btn)
        row2_layout.addWidget(self.export_btn)
        layout.addLayout(row2_layout)
        
        # 第三行按钮
        row3_layout = QHBoxLayout()
        self.reset_view_btn = QPushButton("重置视图")
        self.reset_view_btn.clicked.connect(self.on_reset_view_clicked)
        
        row3_layout.addWidget(self.reset_view_btn)
        layout.addLayout(row3_layout)
        
        # 显示选项
        options_layout = QVBoxLayout()
        self.show_grid_cb = QCheckBox("显示网格")
        self.show_grid_cb.setChecked(True)
        self.show_axes_cb = QCheckBox("显示坐标轴")
        self.show_axes_cb.setChecked(True)
        self.show_labels_cb = QCheckBox("显示标签")
        self.show_labels_cb.setChecked(True)
        
        options_layout.addWidget(self.show_grid_cb)
        options_layout.addWidget(self.show_axes_cb)
        options_layout.addWidget(self.show_labels_cb)
        layout.addLayout(options_layout)
        
        group.setLayout(layout)
        parent_layout.addWidget(group)
    
    def create_statistics_group(self, parent_layout):
        """创建统计信息组"""
        group = QGroupBox("统计信息")
        layout = QVBoxLayout()
        
        # 统计标签
        self.stats_labels = {
            'total': QLabel("总数据包: 0"),
            'valid': QLabel("有效数据: 0"),
            'error': QLabel("错误数据: 0"),
            'rate': QLabel("成功率: 0%"),
            'time': QLabel("运行时间: 00:00:00")
        }
        
        for label in self.stats_labels.values():
            label.setFont(QFont("Consolas", 8))
            layout.addWidget(label)
        
        group.setLayout(layout)
        parent_layout.addWidget(group)
    
    def create_log_group(self, parent_layout):
        """创建日志显示组"""
        group = QGroupBox("状态日志")
        layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(100)
        self.log_text.setFont(QFont("Consolas", 8))
        self.log_text.setReadOnly(True)
        
        layout.addWidget(self.log_text)
        
        group.setLayout(layout)
        parent_layout.addWidget(group)
    
    # 事件处理方法
    def on_connect_clicked(self):
        """连接按钮点击"""
        port = self.port_combo.currentText()
        baudrate = int(self.baud_combo.currentText())
        self.connect_requested.emit(port, baudrate)
    
    def on_disconnect_clicked(self):
        """断开按钮点击"""
        self.disconnect_requested.emit()
    
    def on_pause_clicked(self):
        """暂停按钮点击"""
        self.is_paused = not self.is_paused
        self.pause_toggled.emit(self.is_paused)
        self.pause_btn.setText("继续" if self.is_paused else "暂停")
    
    def on_clear_clicked(self):
        """清空按钮点击"""
        self.clear_data_requested.emit()
    
    def on_record_clicked(self):
        """记录按钮点击"""
        self.is_recording = not self.is_recording
        self.recording_toggled.emit(self.is_recording)
        self.record_btn.setText("停止记录" if self.is_recording else "开始记录")
        
        if self.is_recording:
            self.start_time = datetime.now()
    
    def on_export_clicked(self):
        """导出按钮点击"""
        self.export_requested.emit()
    
    def on_reset_view_clicked(self):
        """重置视图按钮点击"""
        self.view_reset_requested.emit()
    
    # 状态更新方法
    def update_connection_status(self, connected: bool):
        """更新连接状态"""
        self.is_connected = connected
        
        if connected:
            self.connection_status.setText("已连接")
            self.connection_status.setStyleSheet("color: green; font-weight: bold;")
            self.connect_btn.setEnabled(False)
            self.disconnect_btn.setEnabled(True)
        else:
            self.connection_status.setText("未连接")
            self.connection_status.setStyleSheet("color: red; font-weight: bold;")
            self.connect_btn.setEnabled(True)
            self.disconnect_btn.setEnabled(False)
    
    def update_sensor_data(self, sensor_data: SensorData):
        """更新传感器数据显示"""
        self.current_data = sensor_data
        
        # 更新时间戳
        timestamp_str = sensor_data.timestamp.strftime("%H:%M:%S.%f")[:-3]
        self.timestamp_label.setText(f"时间: {timestamp_str}")
        
        # 更新传感器数值
        for i, value in enumerate(sensor_data.sensor_values):
            self.sensor_values[i].setText(f"{value:.2f}")
            
            # 根据数值设置颜色
            if value < 1000:
                color = "lightgreen"
            elif value < 2000:
                color = "yellow"
            elif value < 3000:
                color = "orange"
            else:
                color = "lightcoral"
            
            self.sensor_values[i].setStyleSheet(
                f"background-color: {color}; padding: 2px; border: 1px solid #ccc; font-weight: bold;"
            )
    
    def update_statistics(self, stats: Dict[str, Any]):
        """更新统计信息"""
        self.stats_labels['total'].setText(f"总数据包: {stats.get('total_packets', 0)}")
        self.stats_labels['valid'].setText(f"有效数据: {stats.get('valid_packets', 0)}")
        self.stats_labels['error'].setText(f"错误数据: {stats.get('error_packets', 0)}")
        self.stats_labels['rate'].setText(f"成功率: {stats.get('success_rate', 0):.1f}%")
    
    def update_display(self):
        """定时更新显示"""
        if self.start_time and self.is_recording:
            elapsed = datetime.now() - self.start_time
            elapsed_str = str(elapsed).split('.')[0]  # 去掉微秒
            self.stats_labels['time'].setText(f"运行时间: {elapsed_str}")
    
    def add_log_message(self, message: str):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        
        # 限制日志行数
        if self.log_text.document().blockCount() > 50:
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.Start)
            cursor.select(cursor.BlockUnderCursor)
            cursor.removeSelectedText()
    
    def get_render_options(self) -> Dict[str, bool]:
        """获取渲染选项"""
        return {
            'show_grid': self.show_grid_cb.isChecked(),
            'show_axes': self.show_axes_cb.isChecked(),
            'show_labels': self.show_labels_cb.isChecked()
        }
    
    def update_port_list(self, ports: List[str]):
        """更新端口列表"""
        current_port = self.port_combo.currentText()
        self.port_combo.clear()
        self.port_combo.addItems(ports)
        
        # 尝试恢复之前选择的端口
        index = self.port_combo.findText(current_port)
        if index >= 0:
            self.port_combo.setCurrentIndex(index)
